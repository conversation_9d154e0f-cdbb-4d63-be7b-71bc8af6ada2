# 🚀 Hướng dẫn Deploy Zenera lên Vercel

## 📋 Tổng quan

Dự án Zenera được tổ chức theo cấu trúc **monorepo** với nhiều packages. <PERSON><PERSON> deploy lên Vercel, chúng ta sẽ deploy từ **thư mục root** và cấu hình Vercel để build package webapp.

## 🏗️ Cấu trúc dự án

```
Zenera/                          # ← Root directory (chọn làm root trên Vercel)
├── packages/
│   ├── webapp/                  # Next.js app (sẽ được deploy)
│   ├── sharing/                 # Shared types & utilities
│   ├── ui-components/           # Shared UI components
│   └── api-server/              # Backend (không deploy lên Vercel)
├── package.json                 # Root package.json với workspaces
├── pnpm-workspace.yaml          # PNPM workspace config
└── turbo.json                   # Turbo build config
```

## ⚙️ Cấu hình Vercel

### 1. Tạo Project trên Vercel

1. <PERSON><PERSON><PERSON> cập [vercel.com](https://vercel.com)
2. Click **"New Project"**
3. Import repository GitHub của bạn
4. **QUAN TRỌNG**: Chọn **Root Directory** là `./` (thư mục gốc)

### 2. Cài đặt Build Settings

Trong phần **Build and Output Settings**:

```bash
# Framework Preset
Next.js

# Root Directory
./

# Build Command
pnpm run build:webapp

# Output Directory
packages/webapp/.next

# Install Command
pnpm install

# Node.js Version
18.x
```

**Lưu ý**: Root package.json đã được cấu hình với Next.js dependencies để Vercel có thể nhận diện framework.

### 3. Environment Variables

Thêm các biến môi trường sau trong Vercel Dashboard:

```bash
# Cấu hình ứng dụng
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NEXT_PUBLIC_API_URL=https://api.your-domain.vercel.app

# Tùy chọn: Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

## 🔧 Quy trình Build

Vercel sẽ thực hiện các bước sau:

1. **Clone repository** về Vercel
2. **Install dependencies**: `pnpm install` (cài đặt tất cả workspace dependencies)
3. **Build shared packages**: Turbo tự động build `@zenera/sharing` và `@zenera/ui-components`
4. **Build webapp**: `pnpm run build:webapp` (chỉ build webapp, bỏ qua api-server)
5. **Deploy**: Deploy từ `packages/webapp/.next`

## 📝 Lưu ý quan trọng

### ✅ Ưu điểm của cách tiếp cận này:

- **Đơn giản**: Không cần config phức tạp
- **Tự động**: Vercel tự động resolve workspace dependencies
- **Nhanh**: Build cache được tối ưu
- **Ổn định**: Ít lỗi hơn so với custom config

### ⚠️ Điều cần chú ý:

- **Root Directory**: Phải chọn `./` (root), không phải `packages/webapp`
- **Build Command**: Sử dụng `pnpm run build:webapp` để tránh build api-server
- **Dependencies**: Đảm bảo tất cả dependencies được khai báo đúng trong `package.json`

## 🧪 Test Build Local

Trước khi deploy, hãy test build local:

```bash
# Từ thư mục root Zenera/
pnpm install
cd packages/webapp && pnpm run build

# Hoặc sử dụng turbo
pnpm run build --filter=@zenera/webapp
```

## 🚀 Deploy

### Tự động (Khuyến nghị)

Vercel sẽ tự động deploy khi:
- Push code lên branch `main` (Production)
- Tạo Pull Request (Preview)

### Thủ công

```bash
# Cài đặt Vercel CLI
npm i -g vercel

# Deploy từ thư mục root
cd Zenera/
vercel --prod
```

## 🔍 Troubleshooting

### Lỗi thường gặp:

1. **"Cannot find module @zenera/sharing"**
   - ✅ Đảm bảo build command có `cd packages/webapp &&`
   - ✅ Kiểm tra workspace dependencies trong `package.json`

2. **"Build failed"**
   - ✅ Test build local trước
   - ✅ Kiểm tra Node.js version (18.x)
   - ✅ Xem build logs trên Vercel dashboard

3. **"Page not found"**
   - ✅ Kiểm tra Output Directory: `packages/webapp/.next`
   - ✅ Đảm bảo Next.js app build thành công

### Debug commands:

```bash
# Kiểm tra workspace structure
pnpm list --depth=0

# Test build từng package
pnpm run build --filter=@zenera/sharing
pnpm run build --filter=@zenera/ui-components  
pnpm run build --filter=@zenera/webapp

# Kiểm tra dependencies
cd packages/webapp && pnpm list
```

## 📊 Performance

- **Bundle Size**: ~101kB shared JS + page-specific chunks
- **Build Time**: ~35-40 giây
- **Cold Start**: <1 giây

## 🎯 Kết quả

Sau khi deploy thành công:
- ✅ Website chạy tại domain Vercel
- ✅ Tự động SSL certificate
- ✅ CDN global
- ✅ Preview deployments cho PR
- ✅ Analytics (nếu enable)

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra build logs trên Vercel
2. Test build local với cùng commands
3. Xem documentation Vercel cho monorepo
4. Liên hệ support nếu cần

---

**Chúc bạn deploy thành công! 🎉**

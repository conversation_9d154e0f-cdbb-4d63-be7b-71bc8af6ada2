{"name": "zenera", "version": "1.0.0", "description": "Zenera E-commerce Platform - Production-tested with Medoo integration", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "build:webapp": "turbo run build --filter=@zenera/webapp", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check"}, "dependencies": {"next": "15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"turbo": "^1.13.4", "@types/node": "^20.14.0", "typescript": "^5.5.0", "eslint": "^8.57.0", "prettier": "^3.3.0"}, "engines": {"node": ">=18.16.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@10.2.1"}
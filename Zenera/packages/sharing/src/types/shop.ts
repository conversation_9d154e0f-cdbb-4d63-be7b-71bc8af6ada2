// Shop types for Zenera platform
// Based on zen-buy.be backend schemas

export enum ShopStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_APPROVAL = 'pending_approval',
}

export enum ShopVerificationStatus {
  UNVERIFIED = 'unverified',
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
}

export interface ShopAddress {
  street: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  latitude?: number;
  longitude?: number;
}

export interface ShopContact {
  phone: string;
  email: string;
  website?: string;
  social_media?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
  };
}

export interface ShopSettings {
  auto_accept_orders: boolean;
  processing_time: number; // in days
  return_policy: string;
  shipping_policy: string;
  terms_of_service: string;
  privacy_policy: string;
}

export interface ShopStats {
  total_products: number;
  total_orders: number;
  total_revenue: number;
  avg_rating: number;
  total_reviews: number;
  followers_count: number;
  response_rate: number;
  response_time: number; // in hours
}

export interface Shop {
  _id?: string;
  id?: string;
  name: string;
  slug: string;
  description: string;
  logo?: string;
  banner?: string;
  owner_id: string;
  category: string;
  address: ShopAddress;
  contact: ShopContact;
  status: ShopStatus;
  verification_status: ShopVerificationStatus;
  verification_documents?: string[];
  settings: ShopSettings;
  stats: ShopStats;
  established_date: Date;
  is_featured: boolean;
  featured_until?: Date;
  tags?: string[];
  business_license?: string;
  tax_id?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface ShopProduct {
  _id?: string;
  id?: string;
  shop_id: string;
  product_id: string;
  is_featured: boolean;
  display_order: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ShopFollower {
  _id?: string;
  id?: string;
  shop_id: string;
  user_id: string;
  followed_at: Date;
}

export interface ShopReview {
  _id?: string;
  id?: string;
  shop_id: string;
  user_id: string;
  order_id?: string;
  rating: number;
  comment?: string;
  response?: string;
  response_date?: Date;
  is_verified: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateShopDto {
  name: string;
  slug: string;
  description: string;
  category: string;
  address: ShopAddress;
  contact: ShopContact;
  logo?: string;
  banner?: string;
  business_license?: string;
  tax_id?: string;
}

export interface UpdateShopDto {
  name?: string;
  description?: string;
  logo?: string;
  banner?: string;
  category?: string;
  address?: Partial<ShopAddress>;
  contact?: Partial<ShopContact>;
  settings?: Partial<ShopSettings>;
  tags?: string[];
}

export interface ShopSearchFilters {
  category?: string;
  location?: string;
  rating_min?: number;
  verification_status?: ShopVerificationStatus;
  min_products?: number;
  sort_by?: 'name' | 'rating' | 'followers' | 'products' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

export interface ShopListResponse {
  shops: Shop[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}

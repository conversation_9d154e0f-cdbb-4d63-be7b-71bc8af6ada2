"use client";

import { useState, useEffect } from 'react';
import { Search, ShoppingCart, User, ChevronDown, Menu, X } from 'lucide-react';
import Link from 'next/link';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useLocale } from '@/lib/hooks/use-locale';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useCartStore } from '@/stores/cart-store';
import { useAuthStore } from '@/stores/auth-store';
import LanguageSwitcher from '@/components/i18n/language-switcher';

export function Navbar() {
  const { t } = useZeneraTranslation('navbar');
  const locale = useLocale();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [totalCartItems, setTotalCartItems] = useState(0);
  const { items, getItemCount } = useCartStore();
  const { user, isAuthenticated } = useAuthStore();

  // Fix hydration by updating cart count on client side
  useEffect(() => {
    setTotalCartItems(getItemCount());
  }, [getItemCount, items]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to search page
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  const categories = [
    { id: 1, name: t('categories.electronics'), href: '/categories/electronics' },
    { id: 2, name: t('categories.clothing'), href: '/categories/clothing' },
    { id: 3, name: t('categories.home'), href: '/categories/home' },
    { id: 4, name: t('categories.books'), href: '/categories/books' },
  ];

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      {/* Top bar */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-10 text-sm">
            <div className="flex items-center space-x-4">
              <Link href={`/${locale}/seller`} className="text-gray-600 hover:text-primary">
                {t('sellerChannel')}
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              {isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="text-gray-600">
                      <User className="h-4 w-4 mr-1" />
                      {user?.first_name ? `${user.first_name} ${user.last_name}`.trim() : user?.email}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/${locale}/account`}>{t('account')}</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/${locale}/orders`}>{t('orders')}</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => useAuthStore.getState().logout()}>
                      {t('logout')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link href={`/${locale}/login`} className="text-gray-600 hover:text-primary">
                    {t('login')}
                  </Link>
                  <span className="text-gray-400">|</span>
                  <Link href={`/${locale}/register`} className="text-gray-600 hover:text-primary">
                    {t('register')}
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main navbar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">Z</span>
            </div>
            <span className="ml-2 text-xl font-bold text-gray-900">Zenera</span>
          </Link>

          {/* Categories dropdown */}
          <div className="hidden md:block">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-gray-700">
                  {t('categories.title')}
                  <ChevronDown className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {categories.map((category) => (
                  <DropdownMenuItem key={category.id} asChild>
                    <Link href={category.href}>{category.name}</Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Search bar */}
          <div className="flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="relative">
              <Input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-4 pr-12"
              />
              <Button
                type="submit"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
              >
                <Search className="h-4 w-4" />
              </Button>
            </form>
          </div>

          {/* Cart and mobile menu */}
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/cart`} className="relative">
              <Button variant="ghost" size="sm">
                <ShoppingCart className="h-5 w-5" />
                <div className={`absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white flex items-center justify-center text-xs font-semibold ${totalCartItems > 0 ? 'block' : 'hidden'}`}>
                  {totalCartItems}
                </div>
              </Button>
            </Link>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-gray-200 bg-white">
          <div className="px-4 py-2 space-y-2">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={category.href}
                className="block py-2 text-gray-700 hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}

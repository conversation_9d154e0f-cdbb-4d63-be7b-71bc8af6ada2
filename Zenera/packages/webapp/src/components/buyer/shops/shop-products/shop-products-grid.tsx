"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Heart, 
  ShoppingCart, 
  Star, 
  Eye,
  Percent,
  Package
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';
import type { Product } from '@zenera/sharing';

interface ShopProductsGridProps {
  products: Product[];
  viewMode: 'grid' | 'list';
  locale: string;
}

export function ShopProductsGrid({ products, viewMode, locale }: ShopProductsGridProps) {
  const { t } = useZeneraTranslation();

  if (products.length === 0) {
    return (
      <Card className="p-12 text-center">
        <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('products.noProducts')}
        </h3>
        <p className="text-gray-600">
          {t('products.noProductsDescription')}
        </p>
      </Card>
    );
  }

  return (
    <div className={
      viewMode === 'grid' 
        ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
        : 'space-y-4'
    }>
      {products.map((product) => (
        viewMode === 'grid' ? (
          <ProductGridCard 
            key={product.id} 
            product={product} 
            locale={locale} 
          />
        ) : (
          <ProductListCard 
            key={product.id} 
            product={product} 
            locale={locale} 
          />
        )
      ))}
    </div>
  );
}

// Grid Card Component
interface ProductCardProps {
  product: Product;
  locale: string;
}

function ProductGridCard({ product, locale }: ProductCardProps) {
  const { t } = useZeneraTranslation();
  const { addItem } = useCartStore();
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    addItem(product);
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  const discountPercentage = product.compare_at_price && product.base_price
    ? Math.round(((product.compare_at_price - product.base_price) / product.compare_at_price) * 100)
    : 0;

  return (
    <Link href={`/${locale}/products/${product.slug}`}>
      <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-0">
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden rounded-t-lg">
            <Image
              src={product.images[0] || '/placeholder-product.jpg'}
              alt={product.name}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            
            {/* Discount Badge */}
            {discountPercentage > 0 && (
              <Badge className="absolute top-2 left-2 bg-red-500 text-white">
                <Percent className="w-3 h-3 mr-1" />
                -{discountPercentage}%
              </Badge>
            )}

            {/* Action Buttons */}
            <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                onClick={handleWishlist}
              >
                <Heart className={cn(
                  "w-4 h-4",
                  isWishlisted ? "text-red-500 fill-current" : "text-gray-600"
                )} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
              >
                <Eye className="w-4 h-4 text-gray-600" />
              </Button>
            </div>

            {/* Quick Add to Cart */}
            <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                onClick={handleAddToCart}
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {t('products.addToCart')}
              </Button>
            </div>
          </div>

          {/* Product Info */}
          <div className="p-4">
            <h3 className="font-medium text-gray-900 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors">
              {product.name}
            </h3>

            {/* Rating */}
            <div className="flex items-center gap-1 mb-2">
              <div className="flex items-center">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={cn(
                      "w-3 h-3",
                      i < Math.floor(product.avg_rating) 
                        ? "text-yellow-400 fill-current" 
                        : "text-gray-300"
                    )}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">
                ({product.review_count})
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-blue-600">
                ${product.base_price?.toLocaleString()}
              </span>
              {product.compare_at_price && product.compare_at_price > (product.base_price || 0) && (
                <span className="text-sm text-gray-500 line-through">
                  ${product.compare_at_price.toLocaleString()}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

// List Card Component
function ProductListCard({ product, locale }: ProductCardProps) {
  const { t } = useZeneraTranslation();
  const { addItem } = useCartStore();
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    addItem(product);
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  const discountPercentage = product.compare_at_price && product.base_price
    ? Math.round(((product.compare_at_price - product.base_price) / product.compare_at_price) * 100)
    : 0;

  return (
    <Link href={`/${locale}/products/${product.slug}`}>
      <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex gap-4">
            {/* Product Image */}
            <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg">
              <Image
                src={product.images[0] || '/placeholder-product.jpg'}
                alt={product.name}
                fill
                className="object-cover"
              />
              {discountPercentage > 0 && (
                <Badge className="absolute top-1 left-1 bg-red-500 text-white text-xs">
                  -{discountPercentage}%
                </Badge>
              )}
            </div>

            {/* Product Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors">
                {product.name}
              </h3>

              <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                {product.description}
              </p>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-2">
                <div className="flex items-center">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "w-3 h-3",
                        i < Math.floor(product.avg_rating) 
                          ? "text-yellow-400 fill-current" 
                          : "text-gray-300"
                      )}
                    />
                  ))}
                </div>
                <span className="text-xs text-gray-500">
                  ({product.review_count})
                </span>
              </div>

              {/* Price */}
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-blue-600">
                  ${product.base_price?.toLocaleString()}
                </span>
                {product.compare_at_price && product.compare_at_price > (product.base_price || 0) && (
                  <span className="text-sm text-gray-500 line-through">
                    ${product.compare_at_price.toLocaleString()}
                  </span>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-2 flex-shrink-0">
              <Button
                size="sm"
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={handleWishlist}
              >
                <Heart className={cn(
                  "w-4 h-4",
                  isWishlisted ? "text-red-500 fill-current" : "text-gray-600"
                )} />
              </Button>
              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={handleAddToCart}
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {t('products.addToCart')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

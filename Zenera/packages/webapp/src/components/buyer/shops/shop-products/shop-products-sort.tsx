"use client";

import { But<PERSON> } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Grid, List, SlidersHorizontal } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface ShopProductsSortProps {
  sortBy: string;
  onSortChange: (value: string) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  totalProducts: number;
  locale: string;
}

export function ShopProductsSort({
  sortBy,
  onSortChange,
  viewMode,
  onViewModeChange,
  totalProducts,
  locale
}: ShopProductsSortProps) {
  const { t } = useZeneraTranslation();

  const sortOptions = [
    { value: 'newest', label: t('products.sort.newest') },
    { value: 'oldest', label: t('products.sort.oldest') },
    { value: 'price-low', label: t('products.sort.priceLow') },
    { value: 'price-high', label: t('products.sort.priceHigh') },
    { value: 'rating', label: t('products.sort.rating') },
    { value: 'popular', label: t('products.sort.popular') },
    { value: 'name-az', label: t('products.sort.nameAZ') },
    { value: 'name-za', label: t('products.sort.nameZA') },
  ];

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Results Count */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <SlidersHorizontal className="w-4 h-4" />
            <span>
              {t('products.showing')} <span className="font-medium">{totalProducts}</span> {t('products.results')}
            </span>
          </div>

          <div className="flex items-center gap-4">
            {/* Sort Dropdown */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 whitespace-nowrap">
                {t('products.sortBy')}:
              </span>
              <Select value={sortBy} onValueChange={onSortChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-lg p-1 bg-gray-50">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('grid')}
                className={cn(
                  "h-8 w-8 p-0",
                  viewMode === 'grid' 
                    ? "bg-white shadow-sm text-blue-600" 
                    : "text-gray-500 hover:text-gray-700"
                )}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewModeChange('list')}
                className={cn(
                  "h-8 w-8 p-0",
                  viewMode === 'list' 
                    ? "bg-white shadow-sm text-blue-600" 
                    : "text-gray-500 hover:text-gray-700"
                )}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

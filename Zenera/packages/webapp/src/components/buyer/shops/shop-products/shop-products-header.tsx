"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Star, 
  MapPin, 
  Users, 
  Package, 
  CheckCircle, 
  Heart,
  Share2,
  MessageCircle,
  ArrowLeft
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';
import type { Shop } from '@zenera/sharing';

interface ShopProductsHeaderProps {
  shop: Shop;
  locale: string;
}

export function ShopProductsHeader({ shop, locale }: ShopProductsHeaderProps) {
  const { t } = useZeneraTranslation();

  return (
    <div className="relative">
      {/* Banner Background */}
      <div className="relative h-48 md:h-64 overflow-hidden">
        <Image
          src={shop.banner || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200'}
          alt={shop.name}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
        
        {/* Back Button */}
        <div className="absolute top-4 left-4">
          <Link href={`/${locale}/shops/${shop.slug}`}>
            <Button variant="secondary" size="sm" className="bg-white/90 hover:bg-white">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t('common.back')}
            </Button>
          </Link>
        </div>
      </div>

      {/* Shop Info Overlay */}
      <div className="container mx-auto px-4">
        <Card className="relative -mt-20 md:-mt-24 bg-white/95 backdrop-blur-sm border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Shop Logo */}
              <div className="flex-shrink-0">
                <div className="relative w-20 h-20 md:w-24 md:h-24 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                  <Image
                    src={shop.logo || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200'}
                    alt={shop.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              {/* Shop Details */}
              <div className="flex-1 min-w-0">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-2xl md:text-3xl font-bold text-gray-900 truncate">
                        {shop.name}
                      </h1>
                      {shop.verification_status === 'verified' && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {t('shops.verified')}
                        </Badge>
                      )}
                    </div>

                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {shop.description}
                    </p>

                    {/* Shop Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="font-medium">{shop.stats.avg_rating}</span>
                        <span className="text-gray-500">({shop.stats.total_reviews})</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-blue-500" />
                        <span className="font-medium">{shop.stats.total_products}</span>
                        <span className="text-gray-500">{t('products.title')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-purple-500" />
                        <span className="font-medium">{shop.stats.followers_count.toLocaleString()}</span>
                        <span className="text-gray-500">{t('shops.followers')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-red-500" />
                        <span className="text-gray-600">{shop.address.city}</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-row md:flex-col gap-2">
                    <Button variant="outline" size="sm" className="flex-1 md:flex-none">
                      <Heart className="w-4 h-4 mr-2" />
                      {t('shops.follow')}
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 md:flex-none">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      {t('shops.contact')}
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 md:flex-none">
                      <Share2 className="w-4 h-4 mr-2" />
                      {t('common.share')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Products Page Title */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {t('shops.allProducts')} ({shop.stats.total_products})
              </h2>
              <p className="text-gray-600 mt-1">
                {t('shops.browseAllProducts')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

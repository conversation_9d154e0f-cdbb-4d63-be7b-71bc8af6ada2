"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Filter, 
  X, 
  Star,
  Package,
  DollarSign,
  Tag
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface ShopProductsFiltersProps {
  filters: {
    category: string;
    priceRange: number[];
    inStock: boolean;
    rating: number;
  };
  onFiltersChange: (filters: any) => void;
  locale: string;
}

export function ShopProductsFilters({ 
  filters, 
  onFiltersChange, 
  locale 
}: ShopProductsFiltersProps) {
  const { t } = useZeneraTranslation();
  const [isOpen, setIsOpen] = useState(false);

  // Mock categories for this shop
  const categories = [
    { id: 'smartphones', name: 'Smartphones', count: 45 },
    { id: 'laptops', name: 'Laptops', count: 32 },
    { id: 'tablets', name: 'Tablets', count: 28 },
    { id: 'accessories', name: 'Accessories', count: 156 },
    { id: 'audio', name: 'Audio & Headphones', count: 67 },
    { id: 'gaming', name: 'Gaming', count: 89 },
    { id: 'smart-home', name: 'Smart Home', count: 43 },
  ];

  const ratings = [5, 4, 3, 2, 1];

  const handleCategoryChange = (categoryId: string) => {
    onFiltersChange({
      ...filters,
      category: filters.category === categoryId ? '' : categoryId,
    });
  };

  const handlePriceRangeChange = (value: number[]) => {
    onFiltersChange({
      ...filters,
      priceRange: value,
    });
  };

  const handleInStockChange = (checked: boolean) => {
    onFiltersChange({
      ...filters,
      inStock: checked,
    });
  };

  const handleRatingChange = (rating: number) => {
    onFiltersChange({
      ...filters,
      rating: filters.rating === rating ? 0 : rating,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      category: '',
      priceRange: [0, 1000000],
      inStock: false,
      rating: 0,
    });
  };

  const hasActiveFilters = filters.category || filters.inStock || filters.rating > 0 || 
    (filters.priceRange[0] > 0 || filters.priceRange[1] < 1000000);

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-4">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between"
        >
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            {t('products.filters')}
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {t('products.active')}
              </Badge>
            )}
          </div>
        </Button>
      </div>

      {/* Filters Panel */}
      <div className={cn(
        "space-y-6",
        "lg:block",
        isOpen ? "block" : "hidden"
      )}>
        {/* Filter Header */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="w-5 h-5" />
                {t('products.filters')}
              </CardTitle>
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4 mr-1" />
                  {t('common.clear')}
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>

        {/* Categories */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Tag className="w-4 h-4" />
              {t('products.categories')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={category.id}
                    checked={filters.category === category.id}
                    onCheckedChange={() => handleCategoryChange(category.id)}
                  />
                  <Label
                    htmlFor={category.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    {category.name}
                  </Label>
                </div>
                <span className="text-xs text-gray-500">({category.count})</span>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Price Range */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              {t('products.priceRange')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Slider
              value={filters.priceRange}
              onValueChange={handlePriceRangeChange}
              max={1000000}
              step={10000}
              className="w-full"
            />
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>${filters.priceRange[0].toLocaleString()}</span>
              <span>${filters.priceRange[1].toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>

        {/* Availability */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Package className="w-4 h-4" />
              {t('products.availability')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="in-stock"
                checked={filters.inStock}
                onCheckedChange={handleInStockChange}
              />
              <Label
                htmlFor="in-stock"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                {t('products.inStock')}
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Rating */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Star className="w-4 h-4" />
              {t('products.rating')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {ratings.map((rating) => (
              <div key={rating} className="flex items-center space-x-2">
                <Checkbox
                  id={`rating-${rating}`}
                  checked={filters.rating === rating}
                  onCheckedChange={() => handleRatingChange(rating)}
                />
                <Label
                  htmlFor={`rating-${rating}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex items-center gap-1"
                >
                  <div className="flex items-center">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={cn(
                          "w-3 h-3",
                          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
                        )}
                      />
                    ))}
                  </div>
                  <span className="ml-1">{t('products.andUp')}</span>
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </>
  );
}

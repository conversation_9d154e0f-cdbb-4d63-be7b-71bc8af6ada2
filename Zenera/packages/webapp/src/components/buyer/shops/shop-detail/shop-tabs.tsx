"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Package, Star, Info, Grid, List } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface ShopTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  locale: string;
}

export function ShopTabs({ activeTab, onTabChange, locale }: ShopTabsProps) {
  const { t } = useZeneraTranslation('shop-detail');

  const tabs = [
    {
      id: 'products',
      label: t('tabs.products'),
      icon: Package,
      count: '450+'
    },
    {
      id: 'reviews',
      label: t('tabs.reviews'),
      icon: Star,
      count: '1.2K'
    },
    {
      id: 'info',
      label: t('tabs.info'),
      icon: Info,
      count: null
    }
  ];

  return (
    <div className="mt-8">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={cn(
                  'flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <IconComponent className="w-5 h-5" />
                <span>{tab.label}</span>
                {tab.count && (
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      'ml-1 text-xs',
                      isActive 
                        ? 'bg-blue-100 text-blue-700' 
                        : 'bg-gray-100 text-gray-600'
                    )}
                  >
                    {tab.count}
                  </Badge>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
}

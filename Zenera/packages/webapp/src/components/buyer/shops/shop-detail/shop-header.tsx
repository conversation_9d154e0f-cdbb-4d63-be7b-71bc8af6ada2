"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  MapPin, 
  Calendar,
  CheckCircle,
  Star,
  Users,
  Package,
  Clock,
  Phone,
  Mail,
  Globe
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';
import type { Shop } from '@zenera/sharing';

interface ShopHeaderProps {
  shop: Shop;
  locale: string;
}

export function ShopHeader({ shop, locale }: ShopHeaderProps) {
  const { t } = useZeneraTranslation('shop-detail');
  const [isFollowing, setIsFollowing] = useState(false);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-4 w-4',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long',
    }).format(date);
  };

  return (
    <Card className="overflow-hidden shadow-xl">
      <CardContent className="p-0">
        {/* Shop Banner */}
        <div className="relative h-64 md:h-80 bg-gradient-to-r from-blue-600 to-purple-600">
          <Image
            src={shop.banner || ''}
            alt={shop.name}
            fill
            className="object-cover opacity-90"
          />
          
          {/* Verification Badge */}
          {shop.verification_status === 'verified' && (
            <Badge className="absolute top-6 right-6 bg-green-500 text-white shadow-lg">
              <CheckCircle className="w-4 h-4 mr-1" />
              {t('header.verified')}
            </Badge>
          )}

          {/* Featured Badge */}
          {shop.is_featured && (
            <Badge className="absolute top-6 left-6 bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-lg">
              ⭐ {t('header.featured')}
            </Badge>
          )}

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
        </div>

        {/* Shop Info */}
        <div className="relative px-6 pb-6">
          {/* Shop Logo */}
          <div className="absolute -top-16 left-6">
            <div className="relative w-32 h-32 rounded-2xl overflow-hidden border-4 border-white shadow-xl bg-white">
              <Image
                src={shop.logo || ''}
                alt={shop.name}
                fill
                className="object-cover"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-6 mb-6">
            <Link href={`/${locale}/shops/${shop.slug}/products`}>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                <Package className="w-4 h-4 mr-2" />
                {t('header.viewAllProducts')}
              </Button>
            </Link>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFollowing(!isFollowing)}
              className={cn(
                "transition-all duration-300",
                isFollowing && "bg-blue-50 border-blue-200 text-blue-700"
              )}
            >
              <Heart className={cn(
                'w-4 h-4 mr-2',
                isFollowing && 'fill-current text-blue-600'
              )} />
              {isFollowing ? t('header.following') : t('header.follow')}
              <span className="ml-1 text-xs">
                ({formatNumber(shop.stats.followers_count)})
              </span>
            </Button>

            <Button variant="outline" size="sm">
              <MessageCircle className="w-4 h-4 mr-2" />
              {t('header.chat')}
            </Button>

            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-2" />
              {t('header.share')}
            </Button>
          </div>

          {/* Shop Details */}
          <div className="ml-36 space-y-4">
            {/* Shop Name & Category */}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  {shop.name}
                </h1>
                {shop.verification_status === 'verified' && (
                  <CheckCircle className="w-6 h-6 text-green-500" />
                )}
              </div>
              
              <Badge variant="secondary" className="mb-3 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                {shop.category}
              </Badge>
            </div>

            {/* Description */}
            <p className="text-gray-600 text-lg leading-relaxed max-w-4xl">
              {shop.description}
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 py-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="flex">
                    {renderStars(shop.stats.avg_rating)}
                  </div>
                </div>
                <div className="font-semibold text-gray-900">
                  {shop.stats.avg_rating}
                </div>
                <div className="text-sm text-gray-600">
                  ({formatNumber(shop.stats.total_reviews)} {t('header.reviews')})
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Package className="w-5 h-5 text-blue-600" />
                </div>
                <div className="font-semibold text-gray-900">
                  {formatNumber(shop.stats.total_products)}
                </div>
                <div className="text-sm text-gray-600">
                  {t('header.products')}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="w-5 h-5 text-purple-600" />
                </div>
                <div className="font-semibold text-gray-900">
                  {formatNumber(shop.stats.followers_count)}
                </div>
                <div className="text-sm text-gray-600">
                  {t('header.followers')}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="w-5 h-5 text-green-600" />
                </div>
                <div className="font-semibold text-gray-900">
                  {shop.stats.response_time}h
                </div>
                <div className="text-sm text-gray-600">
                  {t('header.responseTime')}
                </div>
              </div>
            </div>

            {/* Contact & Location */}
            <div className="flex flex-wrap gap-6 text-sm text-gray-600 pt-4 border-t">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>{shop.address.city}, {shop.address.country}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{t('header.established')} {formatDate(shop.established_date)}</span>
              </div>

              {shop.contact.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>{shop.contact.phone}</span>
                </div>
              )}

              {shop.contact.email && (
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span>{shop.contact.email}</span>
                </div>
              )}

              {shop.contact.website && (
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <a 
                    href={shop.contact.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    {t('header.website')}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  ShoppingBag, 
  DollarSign, 
  MessageSquare,
  Clock,
  CheckCircle,
  Award,
  Target
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Shop } from '@zenera/sharing';

interface ShopStatsProps {
  shop: Shop;
  locale: string;
}

export function ShopStats({ shop, locale }: ShopStatsProps) {
  const { t } = useZeneraTranslation('shop-detail');

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      style: 'currency',
      currency: locale === 'vi' ? 'VND' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const stats = [
    {
      icon: ShoppingBag,
      label: t('stats.totalOrders'),
      value: formatNumber(shop.stats.total_orders),
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      trend: '+12%',
      trendColor: 'text-green-600'
    },
    {
      icon: DollarSign,
      label: t('stats.totalRevenue'),
      value: formatCurrency(shop.stats.total_revenue),
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      trend: '+8%',
      trendColor: 'text-green-600'
    },
    {
      icon: MessageSquare,
      label: t('stats.responseRate'),
      value: `${shop.stats.response_rate}%`,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      trend: '+2%',
      trendColor: 'text-green-600'
    },
    {
      icon: Clock,
      label: t('stats.avgResponseTime'),
      value: `${shop.stats.response_time}h`,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      trend: '-0.5h',
      trendColor: 'text-green-600'
    }
  ];

  const achievements = [
    {
      icon: Award,
      label: t('achievements.topSeller'),
      description: t('achievements.topSellerDesc'),
      earned: true
    },
    {
      icon: CheckCircle,
      label: t('achievements.verified'),
      description: t('achievements.verifiedDesc'),
      earned: shop.verification_status === 'verified'
    },
    {
      icon: Target,
      label: t('achievements.fastShipping'),
      description: t('achievements.fastShippingDesc'),
      earned: shop.settings.processing_time <= 2
    },
    {
      icon: TrendingUp,
      label: t('achievements.highRating'),
      description: t('achievements.highRatingDesc'),
      earned: shop.stats.avg_rating >= 4.5
    }
  ];

  return (
    <div className="mt-8 space-y-6">
      {/* Performance Stats */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            {t('stats.title')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="relative">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <IconComponent className={`w-6 h-6 ${stat.color}`} />
                    </div>
                    <Badge variant="secondary" className={`${stat.trendColor} bg-transparent border-0 text-xs`}>
                      {stat.trend}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-600">
                      {stat.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            {t('achievements.title')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {achievements.map((achievement, index) => {
              const IconComponent = achievement.icon;
              return (
                <div 
                  key={index} 
                  className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                    achievement.earned 
                      ? 'border-green-200 bg-green-50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg ${
                      achievement.earned 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    {achievement.earned && (
                      <Badge className="bg-green-500 text-white text-xs">
                        {t('achievements.earned')}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    <div className={`font-semibold ${
                      achievement.earned ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {achievement.label}
                    </div>
                    <div className={`text-xs ${
                      achievement.earned ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {achievement.description}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Info */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            {t('quickInfo.title')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">
                {t('quickInfo.processingTime')}
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {shop.settings.processing_time} {t('quickInfo.days')}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">
                {t('quickInfo.returnPolicy')}
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {shop.settings.return_policy}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">
                {t('quickInfo.shippingPolicy')}
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {shop.settings.shipping_policy}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

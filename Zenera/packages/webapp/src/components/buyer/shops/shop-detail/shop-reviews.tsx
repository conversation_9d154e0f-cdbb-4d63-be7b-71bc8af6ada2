"use client";

import { useState } from 'react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, ThumbsUp, MessageSquare, CheckCircle } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';
import type { Shop } from '@zenera/sharing';

interface ShopReviewsProps {
  shop: Shop;
  locale: string;
}

export function ShopReviews({ shop, locale }: ShopReviewsProps) {
  const { t } = useZeneraTranslation('shop-detail');
  const [helpfulReviews, setHelpfulReviews] = useState<Set<string>>(new Set());

  // Mock reviews data
  const reviews = [
    {
      id: '1',
      user: {
        name: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        verified: true
      },
      rating: 5,
      comment: 'Excellent shop with great customer service! Fast shipping and high-quality products. Highly recommended!',
      date: new Date('2024-01-15'),
      helpful: 12,
      verified_purchase: true,
      product: {
        name: 'Premium Wireless Headphones',
        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100'
      }
    },
    {
      id: '2',
      user: {
        name: 'Sarah Johnson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
        verified: true
      },
      rating: 4,
      comment: 'Good products and reasonable prices. The only issue was slightly delayed shipping, but overall satisfied.',
      date: new Date('2024-01-10'),
      helpful: 8,
      verified_purchase: true,
      product: {
        name: 'Smart Watch Series X',
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=100'
      }
    },
    {
      id: '3',
      user: {
        name: 'Mike Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
        verified: false
      },
      rating: 5,
      comment: 'Amazing shop! Great communication and fast response to questions. Will definitely shop here again.',
      date: new Date('2024-01-05'),
      helpful: 15,
      verified_purchase: true,
      product: {
        name: 'Wireless Charging Pad',
        image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=100'
      }
    }
  ];

  // Rating distribution
  const ratingDistribution = [
    { stars: 5, count: 850, percentage: 68 },
    { stars: 4, count: 280, percentage: 22 },
    { stars: 3, count: 75, percentage: 6 },
    { stars: 2, count: 30, percentage: 2 },
    { stars: 1, count: 15, percentage: 1 },
  ];

  const toggleHelpful = (reviewId: string) => {
    const newHelpful = new Set(helpfulReviews);
    if (newHelpful.has(reviewId)) {
      newHelpful.delete(reviewId);
    } else {
      newHelpful.add(reviewId);
    }
    setHelpfulReviews(newHelpful);
  };

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const starSize = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5';
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          starSize,
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            {t('reviews.summary')}
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Overall Rating */}
            <div className="text-center">
              <div className="text-5xl font-bold text-gray-900 mb-2">
                {shop.stats.avg_rating}
              </div>
              <div className="flex items-center justify-center mb-2">
                {renderStars(shop.stats.avg_rating, 'md')}
              </div>
              <div className="text-gray-600">
                {shop.stats.total_reviews.toLocaleString()} {t('reviews.title')}
              </div>
            </div>

            {/* Rating Distribution */}
            <div>
              <h4 className="font-medium text-gray-900 mb-4">
                {t('reviews.distribution')}
              </h4>
              <div className="space-y-3">
                {ratingDistribution.map((item) => (
                  <div key={item.stars} className="flex items-center gap-3">
                    <div className="flex items-center gap-1 w-12">
                      <span className="text-sm">{item.stars}</span>
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    </div>
                    <Progress value={item.percentage} className="flex-1 h-2" />
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {item.count}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Reviews */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            {t('reviews.recent')}
          </h3>
          
          <div className="space-y-6">
            {reviews.map((review) => (
              <div key={review.id} className="border-b border-gray-100 last:border-b-0 pb-6 last:pb-0">
                <div className="flex items-start gap-4">
                  {/* User Avatar */}
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={review.user.avatar} alt={review.user.name} />
                    <AvatarFallback>
                      {review.user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1">
                    {/* User Info & Rating */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">
                          {review.user.name}
                        </span>
                        {review.user.verified && (
                          <CheckCircle className="w-4 h-4 text-blue-500" />
                        )}
                        {review.verified_purchase && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                            {t('reviews.verified')}
                          </Badge>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {formatDate(review.date)}
                      </span>
                    </div>

                    {/* Rating */}
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex">
                        {renderStars(review.rating)}
                      </div>
                      <span className="text-sm text-gray-600">
                        {review.rating}/5
                      </span>
                    </div>

                    {/* Review Comment */}
                    <p className="text-gray-700 mb-3 leading-relaxed">
                      {review.comment}
                    </p>

                    {/* Product Info */}
                    {review.product && (
                      <div className="flex items-center gap-3 mb-3 p-3 bg-gray-50 rounded-lg">
                        <div className="relative w-12 h-12 rounded-lg overflow-hidden">
                          <Image
                            src={review.product.image}
                            alt={review.product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <span className="text-sm text-gray-600">
                          {review.product.name}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleHelpful(review.id)}
                        className={cn(
                          "text-gray-600 hover:text-blue-600",
                          helpfulReviews.has(review.id) && "text-blue-600"
                        )}
                      >
                        <ThumbsUp className={cn(
                          "w-4 h-4 mr-1",
                          helpfulReviews.has(review.id) && "fill-current"
                        )} />
                        {t('reviews.helpful')} ({review.helpful + (helpfulReviews.has(review.id) ? 1 : 0)})
                      </Button>
                      
                      <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600">
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Reply
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-6">
            <Button variant="outline">
              Load More Reviews
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

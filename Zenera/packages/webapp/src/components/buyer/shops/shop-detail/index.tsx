"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ShopHeader } from './shop-header';
import { ShopStats } from './shop-stats';
import { ShopTabs } from './shop-tabs';
import { ShopProducts } from './shop-products';
import { ShopReviews } from './shop-reviews';
import { ShopInfo } from './shop-info';
import type { Shop } from '@zenera/sharing';

interface ShopProduct {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface ShopDetailPageProps {
  shop: Shop;
  featuredProducts: ShopProduct[];
  locale: string;
  filters?: {
    tab?: string;
    category?: string;
    sort?: string;
    page?: string;
  };
}

export function ShopDetailPage({ shop, featuredProducts, locale, filters = {} }: ShopDetailPageProps) {
  const { t } = useZeneraTranslation('shop-detail');
  const [activeTab, setActiveTab] = useState(filters.tab || 'products');

  const breadcrumbItems = [
    { label: t('breadcrumb.home'), href: `/${locale}` },
    { label: t('breadcrumb.shops'), href: `/${locale}/shops` },
    { label: shop.name },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <SimpleBreadcrumb items={breadcrumbItems} className="mb-6" />

        {/* Shop Header */}
        <ShopHeader shop={shop} locale={locale} />

        {/* Shop Stats */}
        <ShopStats shop={shop} locale={locale} />

        {/* Shop Tabs */}
        <ShopTabs 
          activeTab={activeTab}
          onTabChange={setActiveTab}
          locale={locale}
        />

        {/* Tab Content */}
        <div className="mt-8">
          {activeTab === 'products' && (
            <ShopProducts 
              shop={shop}
              featuredProducts={featuredProducts}
              locale={locale}
              filters={filters}
            />
          )}
          
          {activeTab === 'reviews' && (
            <ShopReviews 
              shop={shop}
              locale={locale}
            />
          )}
          
          {activeTab === 'info' && (
            <ShopInfo 
              shop={shop}
              locale={locale}
            />
          )}
        </div>
      </div>
    </div>
  );
}

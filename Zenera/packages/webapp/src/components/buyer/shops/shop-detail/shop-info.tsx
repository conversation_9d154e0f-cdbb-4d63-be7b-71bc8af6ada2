"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Calendar,
  FileText,
  Shield,
  Truck,
  RotateCcw,
  Facebook,
  Instagram,
  Twitter
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Shop } from '@zenera/sharing';

interface ShopInfoProps {
  shop: Shop;
  locale: string;
}

export function ShopInfo({ shop, locale }: ShopInfoProps) {
  const { t } = useZeneraTranslation('shop-detail');

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const socialIcons = {
    facebook: Facebook,
    instagram: Instagram,
    twitter: Twitter,
  };

  return (
    <div className="space-y-6">
      {/* About Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            {t('info.about')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            {shop.description}
          </p>
          
          {/* Tags */}
          {shop.tags && shop.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {shop.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="bg-blue-100 text-blue-700">
                  #{tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Establishment Date */}
          <div className="flex items-center gap-2 text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>{t('info.established')}: {formatDate(shop.established_date)}</span>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5" />
            {t('info.contact')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Address */}
          <div className="flex items-start gap-3">
            <MapPin className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <div className="font-medium text-gray-900 mb-1">
                {t('info.address')}
              </div>
              <div className="text-gray-600">
                {shop.address.street}<br />
                {shop.address.city}, {shop.address.state}<br />
                {shop.address.country} {shop.address.postal_code}
              </div>
            </div>
          </div>

          {/* Phone */}
          {shop.contact.phone && (
            <div className="flex items-center gap-3">
              <Phone className="w-5 h-5 text-gray-500" />
              <div>
                <div className="font-medium text-gray-900 mb-1">
                  {t('info.phone')}
                </div>
                <a 
                  href={`tel:${shop.contact.phone}`}
                  className="text-blue-600 hover:text-blue-800 transition-colors"
                >
                  {shop.contact.phone}
                </a>
              </div>
            </div>
          )}

          {/* Email */}
          {shop.contact.email && (
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-gray-500" />
              <div>
                <div className="font-medium text-gray-900 mb-1">
                  {t('info.email')}
                </div>
                <a 
                  href={`mailto:${shop.contact.email}`}
                  className="text-blue-600 hover:text-blue-800 transition-colors"
                >
                  {shop.contact.email}
                </a>
              </div>
            </div>
          )}

          {/* Website */}
          {shop.contact.website && (
            <div className="flex items-center gap-3">
              <Globe className="w-5 h-5 text-gray-500" />
              <div>
                <div className="font-medium text-gray-900 mb-1">
                  {t('info.website')}
                </div>
                <a 
                  href={shop.contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors"
                >
                  {shop.contact.website}
                </a>
              </div>
            </div>
          )}

          {/* Social Media */}
          {shop.contact.social_media && Object.keys(shop.contact.social_media).length > 0 && (
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 text-gray-500 mt-0.5">
                📱
              </div>
              <div>
                <div className="font-medium text-gray-900 mb-2">
                  {t('info.socialMedia')}
                </div>
                <div className="flex gap-2">
                  {Object.entries(shop.contact.social_media).map(([platform, url]) => {
                    if (!url) return null;
                    const IconComponent = socialIcons[platform as keyof typeof socialIcons];
                    return (
                      <Button
                        key={platform}
                        variant="outline"
                        size="sm"
                        asChild
                      >
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-2"
                        >
                          {IconComponent && <IconComponent className="w-4 h-4" />}
                          {platform.charAt(0).toUpperCase() + platform.slice(1)}
                        </a>
                      </Button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Policies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t('info.policies')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Return Policy */}
          <div className="flex items-start gap-3">
            <RotateCcw className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <div className="font-medium text-gray-900 mb-1">
                {t('info.returnPolicy')}
              </div>
              <div className="text-gray-600">
                {shop.settings.return_policy}
              </div>
            </div>
          </div>

          {/* Shipping Policy */}
          <div className="flex items-start gap-3">
            <Truck className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <div className="font-medium text-gray-900 mb-1">
                {t('info.shippingPolicy')}
              </div>
              <div className="text-gray-600">
                {shop.settings.shipping_policy}
              </div>
            </div>
          </div>

          {/* Processing Time */}
          <div className="flex items-start gap-3">
            <Calendar className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <div className="font-medium text-gray-900 mb-1">
                Processing Time
              </div>
              <div className="text-gray-600">
                {shop.settings.processing_time} days
              </div>
            </div>
          </div>

          {/* Terms & Privacy */}
          <div className="pt-4 border-t">
            <div className="flex gap-4">
              <Button variant="outline" size="sm">
                {t('info.termsOfService')}
              </Button>
              <Button variant="outline" size="sm">
                {t('info.privacyPolicy')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            {t('info.businessInfo')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Business License */}
          {shop.business_license && (
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">
                  {t('info.businessLicense')}
                </div>
                <div className="text-gray-600">
                  {shop.business_license}
                </div>
              </div>
              <Badge className="bg-green-100 text-green-700">
                Verified
              </Badge>
            </div>
          )}

          {/* Tax ID */}
          {shop.tax_id && (
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">
                  {t('info.taxId')}
                </div>
                <div className="text-gray-600">
                  {shop.tax_id}
                </div>
              </div>
              <Badge className="bg-green-100 text-green-700">
                Verified
              </Badge>
            </div>
          )}

          {/* Verification Status */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div>
              <div className="font-medium text-gray-900">
                Verification Status
              </div>
              <div className="text-gray-600">
                Identity and business documents verified
              </div>
            </div>
            <Badge className="bg-green-100 text-green-700">
              {shop.verification_status === 'verified' ? 'Verified' : 'Pending'}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

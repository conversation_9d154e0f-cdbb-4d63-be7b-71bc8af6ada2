"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  Heart, 
  ShoppingCart, 
  Star, 
  Grid, 
  List, 
  Search,
  SlidersHorizontal
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';
import type { Shop } from '@zenera/sharing';

interface ShopProduct {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface ShopProductsProps {
  shop: Shop;
  featuredProducts: ShopProduct[];
  locale: string;
  filters?: {
    category?: string;
    sort?: string;
    page?: string;
  };
}

export function ShopProducts({ shop, featuredProducts, locale, filters = {} }: ShopProductsProps) {
  const { t } = useZeneraTranslation('shop-detail');
  const { addItem } = useCartStore();
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(filters.category || '');
  const [sortBy, setSortBy] = useState(filters.sort || 'newest');
  const [inStockOnly, setInStockOnly] = useState(false);
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());

  // Mock categories
  const categories = [
    { id: 'all', name: t('products.all'), count: 450 },
    { id: 'electronics', name: 'Electronics', count: 180 },
    { id: 'accessories', name: 'Accessories', count: 120 },
    { id: 'audio', name: 'Audio', count: 89 },
    { id: 'mobile', name: 'Mobile', count: 61 },
  ];

  // Mock all products (in real app, this would be fetched from API)
  const allProducts = [
    ...featuredProducts,
    // Add more mock products
    {
      id: '5',
      name: 'Gaming Keyboard RGB',
      slug: 'gaming-keyboard-rgb',
      base_price: 89.99,
      sale_price: 69.99,
      images: ['https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500'],
      category: 'Electronics',
      rating: 4.4,
      reviews_count: 67,
      in_stock: true,
    },
    {
      id: '6',
      name: 'Wireless Mouse Pro',
      slug: 'wireless-mouse-pro',
      base_price: 59.99,
      images: ['https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500'],
      category: 'Electronics',
      rating: 4.2,
      reviews_count: 45,
      in_stock: true,
    },
  ];

  const toggleWishlist = (productId: string) => {
    const newWishlist = new Set(wishlist);
    if (newWishlist.has(productId)) {
      newWishlist.delete(productId);
    } else {
      newWishlist.add(productId);
    }
    setWishlist(newWishlist);
  };

  const handleAddToCart = async (product: ShopProduct) => {
    try {
      await addItem(product as any);
      // TODO: Show success toast
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      style: 'currency',
      currency: locale === 'vi' ? 'VND' : 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-4 w-4',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Featured Products Section */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          {t('products.featured')}
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredProducts.slice(0, 4).map((product) => (
            <Card key={product.id} className="group overflow-hidden hover-lift border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-white rounded-xl">
              <CardContent className="p-0">
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden rounded-t-xl">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  {product.sale_price && (
                    <Badge className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                      Sale
                    </Badge>
                  )}
                  
                  {/* Hover Actions */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="glass text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"
                        onClick={() => toggleWishlist(product.id)}
                      >
                        <Heart 
                          className={cn(
                            'h-4 w-4',
                            wishlist.has(product.id) && 'fill-current text-red-400'
                          )}
                        />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <Badge variant="secondary" className="mb-2 text-xs bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                    {product.category}
                  </Badge>
                  
                  <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
                    <Link 
                      href={`/${locale}/products/${product.slug}`}
                      className="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300"
                    >
                      {product.name}
                    </Link>
                  </h4>

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-2">
                    <div className="flex">
                      {renderStars(product.rating)}
                    </div>
                    <span className="text-xs text-gray-600">
                      ({product.reviews_count})
                    </span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2 mb-3">
                    <span className="font-bold text-gray-900 text-sm">
                      {formatPrice(product.sale_price || product.base_price)}
                    </span>
                    {product.sale_price && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(product.base_price)}
                      </span>
                    )}
                  </div>

                  {/* Add to Cart Button */}
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.in_stock}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                    size="sm"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* All Products Section */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            {t('products.all')}
          </h3>
          
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search products..."
                className="pl-10 w-64"
              />
            </div>
            
            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={t('products.sort.label')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{t('products.sort.newest')}</SelectItem>
                <SelectItem value="oldest">{t('products.sort.oldest')}</SelectItem>
                <SelectItem value="priceHigh">{t('products.sort.priceHigh')}</SelectItem>
                <SelectItem value="priceLow">{t('products.sort.priceLow')}</SelectItem>
                <SelectItem value="popular">{t('products.sort.popular')}</SelectItem>
                <SelectItem value="rating">{t('products.sort.rating')}</SelectItem>
              </SelectContent>
            </Select>
            
            {/* View Mode */}
            <div className="flex items-center border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Filters Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Categories */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">{t('products.categories')}</h4>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={category.id}
                            checked={selectedCategory === category.id}
                            onCheckedChange={(checked) => setSelectedCategory(checked ? category.id : '')}
                          />
                          <Label htmlFor={category.id} className="text-sm cursor-pointer">
                            {category.name}
                          </Label>
                        </div>
                        <span className="text-xs text-gray-500">({category.count})</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Availability filter */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">{t('products.filters.availability')}</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="inStock"
                      checked={inStockOnly}
                      onCheckedChange={(checked) => setInStockOnly(checked === true)}
                    />
                    <Label htmlFor="inStock" className="text-sm cursor-pointer">
                      {t('products.filters.inStock')}
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allProducts.map((product) => (
            <Card key={product.id} className="group overflow-hidden hover-lift border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-white rounded-xl">
              <CardContent className="p-0">
                <div className="relative aspect-square overflow-hidden rounded-t-xl">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  {product.sale_price && (
                    <Badge className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                      Sale
                    </Badge>
                  )}
                </div>
                <div className="p-4">
                  <Badge variant="secondary" className="mb-2 text-xs bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                    {product.category}
                  </Badge>
                  <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
                    <Link 
                      href={`/${locale}/products/${product.slug}`}
                      className="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300"
                    >
                      {product.name}
                    </Link>
                  </h4>
                  <div className="flex items-center gap-1 mb-2">
                    <div className="flex">
                      {renderStars(product.rating)}
                    </div>
                    <span className="text-xs text-gray-600">
                      ({product.reviews_count})
                    </span>
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className="font-bold text-gray-900 text-sm">
                      {formatPrice(product.sale_price || product.base_price)}
                    </span>
                    {product.sale_price && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(product.base_price)}
                      </span>
                    )}
                  </div>
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.in_stock}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                    size="sm"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Results info */}
        <div className="mt-6 text-center text-gray-600">
          {t('products.results.showing', { count: allProducts.length })}
        </div>
      </div>
    </div>
  );
}

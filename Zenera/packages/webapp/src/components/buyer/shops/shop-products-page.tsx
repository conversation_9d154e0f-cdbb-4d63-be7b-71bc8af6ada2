"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ShopProductsHeader } from './shop-products/shop-products-header';
import { ShopProductsFilters } from './shop-products/shop-products-filters';
import { ShopProductsGrid } from './shop-products/shop-products-grid';
import { ShopProductsSort } from './shop-products/shop-products-sort';
import type { Shop, Product } from '@zenera/sharing';
import { ShopStatus, ShopVerificationStatus } from '@zenera/sharing';

interface ShopProductsPageProps {
  locale: string;
  shopSlug: string;
}

export function ShopProductsPage({ locale, shopSlug }: ShopProductsPageProps) {
  const { t } = useZeneraTranslation();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [filters, setFilters] = useState({
    category: '',
    priceRange: [0, 1000000],
    inStock: false,
    rating: 0,
  });

  // Mock shop data - will be replaced with API call
  const mockShop: Shop = {
    _id: 'shop-1',
    id: 'shop-1',
    name: 'TechWorld Store',
    slug: 'techworld-store',
    description: 'Your one-stop destination for the latest technology and gadgets.',
    logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200',
    banner: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200',
    owner_id: 'user-1',
    category: 'Electronics',
    address: {
      street: '123 Tech Street',
      city: 'Ho Chi Minh City',
      state: 'Ho Chi Minh',
      country: 'Vietnam',
      postal_code: '700000',
      latitude: 10.8231,
      longitude: 106.6297,
    },
    contact: {
      phone: '+84 123 456 789',
      email: '<EMAIL>',
      website: 'https://techworld.com',
      social_media: {
        facebook: 'https://facebook.com/techworld',
        instagram: 'https://instagram.com/techworld',
      },
    },
    status: ShopStatus.ACTIVE,
    verification_status: ShopVerificationStatus.VERIFIED,
    settings: {
      auto_accept_orders: true,
      processing_time: 2,
      return_policy: '30 days return policy',
      shipping_policy: 'Free shipping on orders over $50',
      terms_of_service: 'Standard terms apply',
      privacy_policy: 'We protect your privacy',
    },
    stats: {
      total_products: 450,
      total_orders: 2500,
      total_revenue: 125000,
      avg_rating: 4.8,
      total_reviews: 1250,
      followers_count: 15600,
      response_rate: 98,
      response_time: 2,
    },
    established_date: new Date('2018-01-01'),
    is_featured: true,
    tags: ['electronics', 'gadgets', 'tech'],
    business_license: 'BL123456789',
    tax_id: 'TX987654321',
    created_at: new Date('2018-01-01'),
    updated_at: new Date(),
  };

  // Mock products data - will be replaced with API call
  const mockProducts: Product[] = [
    {
      id: 'product-1',
      name: 'iPhone 15 Pro Max',
      slug: 'iphone-15-pro-max',
      description: 'Latest iPhone with advanced features',
      category_id: 'smartphones',
      brand: 'Apple',
      tags: ['smartphone', 'apple', 'premium'],
      images: [
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
        'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',
      ],
      is_active: true,
      attributes: [],
      base_price: 1199,
      compare_at_price: 1299,
      avg_rating: 4.8,
      review_count: 245,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: 'product-2',
      name: 'Samsung Galaxy S24 Ultra',
      slug: 'samsung-galaxy-s24-ultra',
      description: 'Premium Android smartphone with S Pen',
      category_id: 'smartphones',
      brand: 'Samsung',
      tags: ['smartphone', 'samsung', 'android'],
      images: [
        'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
        'https://images.unsplash.com/photo-1580910051074-3eb694886505?w=500',
      ],
      is_active: true,
      attributes: [],
      base_price: 1099,
      compare_at_price: 1199,
      avg_rating: 4.7,
      review_count: 189,
      created_at: new Date(),
      updated_at: new Date(),
    },
    // Add more mock products...
  ];

  const breadcrumbItems = [
    { label: t('common.home'), href: `/${locale}` },
    { label: t('shops.title'), href: `/${locale}/shops` },
    { label: mockShop.name, href: `/${locale}/shops/${shopSlug}` },
    { label: t('products.title'), href: `/${locale}/shops/${shopSlug}/products` },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Shop Products Header */}
      <ShopProductsHeader shop={mockShop} locale={locale} />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-1/4">
            <ShopProductsFilters
              filters={filters}
              onFiltersChange={setFilters}
              locale={locale}
            />
          </div>

          {/* Products Content */}
          <div className="lg:w-3/4">
            {/* Sort and View Controls */}
            <ShopProductsSort
              sortBy={sortBy}
              onSortChange={setSortBy}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              totalProducts={mockProducts.length}
              locale={locale}
            />

            {/* Products Grid */}
            <ShopProductsGrid
              products={mockProducts}
              viewMode={viewMode}
              locale={locale}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

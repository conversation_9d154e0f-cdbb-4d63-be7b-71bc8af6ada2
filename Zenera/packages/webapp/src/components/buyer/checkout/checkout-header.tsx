"use client";

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, ArrowLeft, Shield, Lock } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CheckoutHeaderProps {
  onBackToCart: () => void;
}

export function CheckoutHeader({ onBackToCart }: CheckoutHeaderProps) {
  const { t } = useZeneraTranslation('checkout');

  return (
    <>
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-green-100 rounded-xl">
            <CreditCard className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              {t('title')}
            </h1>
            <p className="text-gray-600 mt-1">
              {t('secureCheckout')}
            </p>
          </div>
        </div>

        <Button 
          variant="outline" 
          onClick={onBackToCart} 
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          {t('backToCart')}
        </Button>
      </div>

      {/* Security Notice */}
      <Alert className="mb-8 border-green-200 bg-green-50">
        <Shield className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <div className="flex items-center gap-2">
            <Lock className="w-4 h-4" />
            {t('securityNotice')}
          </div>
        </AlertDescription>
      </Alert>
    </>
  );
}

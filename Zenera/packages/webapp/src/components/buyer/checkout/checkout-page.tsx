"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Shield, 
  Truck,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  Lock
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { ShippingForm } from './shipping-form';
import { PaymentForm } from './payment-form';
import { OrderSummary } from './order-summary';
// import { CheckoutSteps } from './checkout-steps'; // Will create this component

interface CheckoutPageProps {
  locale: string;
}

export function CheckoutPage({ locale }: CheckoutPageProps) {
  const { t } = useZeneraTranslation('checkout');
  const router = useRouter();
  const { items, getTotalPrice, getTotalItems } = useCartStore();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [shippingData, setShippingData] = useState<any>(null);
  const [paymentData, setPaymentData] = useState<any>(null);
  const [orderError, setOrderError] = useState<string | null>(null);

  const breadcrumbItems = [
    { label: t('home'), href: `/${locale}` },
    { label: t('cart'), href: `/${locale}/cart` },
    { label: t('title'), href: `/${locale}/checkout` },
  ];

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      router.push(`/${locale}/cart`);
    }
  }, [items.length, locale, router]);

  const handleShippingSubmit = (data: any) => {
    setShippingData(data);
    setCurrentStep(2);
  };

  const handlePaymentSubmit = (data: any) => {
    setPaymentData(data);
    setCurrentStep(3);
  };

  const handlePlaceOrder = async () => {
    if (!shippingData || !paymentData) {
      setOrderError(t('missingInformation'));
      return;
    }

    setIsProcessing(true);
    setOrderError(null);

    try {
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock order creation
      const orderData = {
        id: `ORDER-${Date.now()}`,
        items,
        shipping: shippingData,
        payment: paymentData,
        total: totalPrice,
        status: 'confirmed',
        createdAt: new Date(),
      };

      // Clear cart after successful order
      // clearCart(); // Uncomment when implementing
      
      // Redirect to order confirmation
      router.push(`/${locale}/orders/${orderData.id}?status=success`);
      
    } catch (error) {
      setOrderError(t('orderFailed'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBackToCart = () => {
    router.push(`/${locale}/cart`);
  };

  const handleEditStep = (step: number) => {
    setCurrentStep(step);
  };

  if (items.length === 0) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Header */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CreditCard className="w-8 h-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('title')}
              </h1>
              <p className="text-gray-600 mt-1">
                {t('secureCheckout')}
              </p>
            </div>
          </div>

          <Button variant="outline" onClick={handleBackToCart} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            {t('backToCart')}
          </Button>
        </div>

        {/* Security Notice */}
        <Alert className="mb-8 border-green-200 bg-green-50">
          <Shield className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="flex items-center gap-2">
              <Lock className="w-4 h-4" />
              {t('securityNotice')}
            </div>
          </AlertDescription>
        </Alert>

        {/* Checkout Steps - Simple inline version */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={`w-16 h-1 mx-2 ${
                    step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Main Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Step 1: Shipping Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    {t('shippingInformation')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ShippingForm 
                    onSubmit={handleShippingSubmit}
                    locale={locale}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 2: Payment Method */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    {t('paymentMethod')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PaymentForm 
                    onSubmit={handlePaymentSubmit}
                    onBack={() => setCurrentStep(1)}
                    locale={locale}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 3: Review & Place Order */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    {t('reviewOrder')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Order Error */}
                  {orderError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{orderError}</AlertDescription>
                    </Alert>
                  )}

                  {/* Shipping Summary */}
                  {shippingData && (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-medium mb-2">{t('shippingAddress')}</h3>
                      <p className="text-sm text-gray-600">
                        {shippingData.firstName} {shippingData.lastName}<br />
                        {shippingData.address}<br />
                        {shippingData.city}, {shippingData.state} {shippingData.zipCode}<br />
                        {shippingData.country}
                      </p>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleEditStep(1)}
                        className="mt-2"
                      >
                        Edit
                      </Button>
                    </div>
                  )}

                  {/* Payment Summary */}
                  {paymentData && (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-medium mb-2">{t('paymentMethod')}</h3>
                      <p className="text-sm text-gray-600">
                        {paymentData.method === 'card' && `**** **** **** ${paymentData.cardNumber?.slice(-4)}`}
                        {paymentData.method === 'paypal' && 'PayPal'}
                        {paymentData.method === 'bank' && t('bankTransfer')}
                      </p>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleEditStep(2)}
                        className="mt-2"
                      >
                        Edit
                      </Button>
                    </div>
                  )}

                  {/* Place Order Button */}
                  <Button
                    onClick={handlePlaceOrder}
                    disabled={isProcessing}
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-medium"
                  >
                    {isProcessing ? (
                      t('processing')
                    ) : (
                      `${t('placeOrder')} - $${totalPrice.toFixed(2)}`
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <OrderSummary 
                items={items}
                totalPrice={totalPrice}
                totalItems={totalItems}
                locale={locale}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

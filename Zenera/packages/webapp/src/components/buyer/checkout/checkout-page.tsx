"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreditCard, Truck, CheckCircle } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { CheckoutHeader } from './checkout-header';
import { CheckoutSteps } from './checkout-steps';
import { ShippingForm } from './shipping-form';
import { PaymentForm } from './payment-form';
import { OrderSummary } from './order-summary';
import { OrderReview } from './order-review';

interface CheckoutPageProps {
  locale: string;
}

export function CheckoutPage({ locale }: CheckoutPageProps) {
  const { t } = useZeneraTranslation('checkout');
  const router = useRouter();
  const { items, getTotalPrice, getTotalItems } = useCartStore();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [shippingData, setShippingData] = useState<any>(null);
  const [paymentData, setPaymentData] = useState<any>(null);
  const [orderError, setOrderError] = useState<string | null>(null);

  const breadcrumbItems = [
    { label: t('home'), href: `/${locale}` },
    { label: t('cart'), href: `/${locale}/cart` },
    { label: t('title'), href: `/${locale}/checkout` },
  ];

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      router.push(`/${locale}/cart`);
    }
  }, [items.length, locale, router]);

  const handleShippingSubmit = (data: any) => {
    setShippingData(data);
    setCurrentStep(2);
  };

  const handlePaymentSubmit = (data: any) => {
    setPaymentData(data);
    setCurrentStep(3);
  };

  const handlePlaceOrder = async () => {
    if (!shippingData || !paymentData) {
      setOrderError(t('missingInformation'));
      return;
    }

    setIsProcessing(true);
    setOrderError(null);

    try {
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock order creation
      const orderData = {
        id: `ORDER-${Date.now()}`,
        items,
        shipping: shippingData,
        payment: paymentData,
        total: totalPrice,
        status: 'confirmed',
        createdAt: new Date(),
      };

      // Clear cart after successful order
      // clearCart(); // Uncomment when implementing
      
      // Redirect to order confirmation
      router.push(`/${locale}/orders/${orderData.id}?status=success`);
      
    } catch (error) {
      setOrderError(t('orderFailed'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBackToCart = () => {
    router.push(`/${locale}/cart`);
  };

  const handleEditStep = (step: number) => {
    setCurrentStep(step);
  };

  if (items.length === 0) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Header */}
      <div className="container mx-auto px-4 py-8">
        <CheckoutHeader onBackToCart={handleBackToCart} />
        <CheckoutSteps currentStep={currentStep} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Main Checkout Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Step 1: Shipping Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    {t('shippingInformation')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ShippingForm 
                    onSubmit={handleShippingSubmit}
                    locale={locale}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 2: Payment Method */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    {t('paymentMethod')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PaymentForm 
                    onSubmit={handlePaymentSubmit}
                    onBack={() => setCurrentStep(1)}
                    locale={locale}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 3: Review & Place Order */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    {t('reviewOrder')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <OrderReview
                    shippingData={shippingData}
                    paymentData={paymentData}
                    orderError={orderError}
                    isProcessing={isProcessing}
                    totalPrice={totalPrice}
                    onEditStep={handleEditStep}
                    onPlaceOrder={handlePlaceOrder}
                  />
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <OrderSummary 
                items={items}
                totalPrice={totalPrice}
                totalItems={totalItems}
                locale={locale}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

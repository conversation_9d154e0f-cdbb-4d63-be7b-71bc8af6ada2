"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  MapPin, 
  User, 
  Phone, 
  Mail,
  Truck,
  Clock
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShippingFormProps {
  onSubmit: (data: any) => void;
  locale: string;
}

interface ShippingFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  apartment?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  shippingMethod: string;
  saveAddress: boolean;
}

export function ShippingForm({ onSubmit, locale }: ShippingFormProps) {
  const { t } = useZeneraTranslation('checkout');
  
  const [formData, setFormData] = useState<ShippingFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    apartment: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    shippingMethod: 'standard',
    saveAddress: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const countries = [
    { value: 'US', label: 'United States' },
    { value: 'VN', label: 'Vietnam' },
    { value: 'CA', label: 'Canada' },
    { value: 'GB', label: 'United Kingdom' },
    { value: 'AU', label: 'Australia' },
  ];

  const shippingMethods = [
    {
      id: 'standard',
      name: t('checkout.standardShipping'),
      description: t('checkout.standardDescription'),
      price: 9.99,
      duration: '5-7 business days',
      icon: Truck
    },
    {
      id: 'express',
      name: t('checkout.expressShipping'),
      description: t('checkout.expressDescription'),
      price: 19.99,
      duration: '2-3 business days',
      icon: Clock
    },
    {
      id: 'overnight',
      name: t('checkout.overnightShipping'),
      description: t('checkout.overnightDescription'),
      price: 39.99,
      duration: 'Next business day',
      icon: Clock
    }
  ];

  const handleInputChange = (field: keyof ShippingFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('checkout.firstNameRequired');
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('checkout.lastNameRequired');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('checkout.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('checkout.emailInvalid');
    }
    if (!formData.phone.trim()) {
      newErrors.phone = t('checkout.phoneRequired');
    }
    if (!formData.address.trim()) {
      newErrors.address = t('checkout.addressRequired');
    }
    if (!formData.city.trim()) {
      newErrors.city = t('checkout.cityRequired');
    }
    if (!formData.state.trim()) {
      newErrors.state = t('checkout.stateRequired');
    }
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = t('checkout.zipCodeRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSubmit(formData);
    } catch (error) {
      console.error('Shipping form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <User className="w-5 h-5" />
          {t('checkout.contactInformation')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">{t('checkout.firstName')} *</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={errors.firstName ? 'border-red-500' : ''}
            />
            {errors.firstName && (
              <p className="text-sm text-red-600 mt-1">{errors.firstName}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="lastName">{t('checkout.lastName')} *</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={errors.lastName ? 'border-red-500' : ''}
            />
            {errors.lastName && (
              <p className="text-sm text-red-600 mt-1">{errors.lastName}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="email">{t('checkout.email')} *</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
              />
            </div>
            {errors.email && (
              <p className="text-sm text-red-600 mt-1">{errors.email}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="phone">{t('checkout.phone')} *</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
              />
            </div>
            {errors.phone && (
              <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
            )}
          </div>
        </div>
      </div>

      <Separator />

      {/* Shipping Address */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          {t('checkout.shippingAddress')}
        </h3>
        
        <div>
          <Label htmlFor="address">{t('checkout.address')} *</Label>
          <Input
            id="address"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={errors.address ? 'border-red-500' : ''}
            placeholder={t('checkout.addressPlaceholder')}
          />
          {errors.address && (
            <p className="text-sm text-red-600 mt-1">{errors.address}</p>
          )}
        </div>

        <div>
          <Label htmlFor="apartment">{t('checkout.apartment')}</Label>
          <Input
            id="apartment"
            value={formData.apartment}
            onChange={(e) => handleInputChange('apartment', e.target.value)}
            placeholder={t('checkout.apartmentPlaceholder')}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="city">{t('checkout.city')} *</Label>
            <Input
              id="city"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className={errors.city ? 'border-red-500' : ''}
            />
            {errors.city && (
              <p className="text-sm text-red-600 mt-1">{errors.city}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="state">{t('checkout.state')} *</Label>
            <Input
              id="state"
              value={formData.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              className={errors.state ? 'border-red-500' : ''}
            />
            {errors.state && (
              <p className="text-sm text-red-600 mt-1">{errors.state}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="zipCode">{t('checkout.zipCode')} *</Label>
            <Input
              id="zipCode"
              value={formData.zipCode}
              onChange={(e) => handleInputChange('zipCode', e.target.value)}
              className={errors.zipCode ? 'border-red-500' : ''}
            />
            {errors.zipCode && (
              <p className="text-sm text-red-600 mt-1">{errors.zipCode}</p>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="country">{t('checkout.country')} *</Label>
          <Select value={formData.country} onValueChange={(value) => handleInputChange('country', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {countries.map((country) => (
                <SelectItem key={country.value} value={country.value}>
                  {country.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Separator />

      {/* Shipping Method */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('checkout.shippingMethod')}</h3>
        
        <div className="space-y-3">
          {shippingMethods.map((method) => {
            const Icon = method.icon;
            return (
              <Card 
                key={method.id}
                className={`cursor-pointer transition-colors ${
                  formData.shippingMethod === method.id 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'hover:border-gray-300'
                }`}
                onClick={() => handleInputChange('shippingMethod', method.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        formData.shippingMethod === method.id 
                          ? 'border-blue-500 bg-blue-500' 
                          : 'border-gray-300'
                      }`}>
                        {formData.shippingMethod === method.id && (
                          <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                        )}
                      </div>
                      <Icon className="w-5 h-5 text-gray-600" />
                      <div>
                        <h4 className="font-medium">{method.name}</h4>
                        <p className="text-sm text-gray-600">{method.description}</p>
                        <p className="text-xs text-gray-500">{method.duration}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${method.price}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Save Address Option */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="saveAddress"
          checked={formData.saveAddress}
          onCheckedChange={(checked) => handleInputChange('saveAddress', checked === true)}
        />
        <Label htmlFor="saveAddress" className="text-sm">
          {t('checkout.saveAddressForFuture')}
        </Label>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
      >
        {isSubmitting ? t('checkout.processing') : t('checkout.continueToPayment')}
      </Button>
    </form>
  );
}

"use client";

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface OrderReviewProps {
  shippingData: any;
  paymentData: any;
  orderError: string | null;
  isProcessing: boolean;
  totalPrice: number;
  onEditStep: (step: number) => void;
  onPlaceOrder: () => void;
}

export function OrderReview({
  shippingData,
  paymentData,
  orderError,
  isProcessing,
  totalPrice,
  onEditStep,
  onPlaceOrder
}: OrderReviewProps) {
  const { t } = useZeneraTranslation('checkout');

  return (
    <div className="space-y-6">
      {/* Order Error */}
      {orderError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{orderError}</AlertDescription>
        </Alert>
      )}

      {/* Shipping Summary */}
      {shippingData && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <h3 className="font-medium mb-2">{t('shippingAddress')}</h3>
              <p className="text-sm text-gray-600">
                {shippingData.firstName} {shippingData.lastName}<br />
                {shippingData.address}<br />
                {shippingData.city}, {shippingData.state} {shippingData.zipCode}<br />
                {shippingData.country}
              </p>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onEditStep(1)}
              className="self-start sm:self-center"
            >
              {t('Edit')}
            </Button>
          </div>
        </div>
      )}

      {/* Payment Summary */}
      {paymentData && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <h3 className="font-medium mb-2">{t('paymentMethod')}</h3>
              <p className="text-sm text-gray-600">
                {paymentData.method === 'card' && `**** **** **** ${paymentData.cardNumber?.slice(-4)}`}
                {paymentData.method === 'paypal' && 'PayPal'}
                {paymentData.method === 'bank' && t('bankTransfer')}
              </p>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onEditStep(2)}
              className="self-start sm:self-center"
            >
              {t('Edit')}
            </Button>
          </div>
        </div>
      )}

      {/* Place Order Button */}
      <Button
        onClick={onPlaceOrder}
        disabled={isProcessing}
        className="w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-medium"
      >
        {isProcessing ? (
          t('processing')
        ) : (
          `${t('placeOrder')} - $${totalPrice.toFixed(2)}`
        )}
      </Button>
    </div>
  );
}

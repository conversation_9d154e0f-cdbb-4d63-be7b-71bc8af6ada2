"use client";

import Image from 'next/image';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingBag, 
  Truck, 
  Shield,
  Tag
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface OrderSummaryProps {
  items: any[];
  totalPrice: number;
  totalItems: number;
  locale: string;
}

export function OrderSummary({ items, totalPrice, totalItems, locale }: OrderSummaryProps) {
  const { t } = useZeneraTranslation();

  // Mock calculations
  const subtotal = totalPrice;
  const shipping = subtotal >= 50 ? 0 : 9.99; // Free shipping over $50
  const tax = subtotal * 0.1; // 10% tax
  const discount = 0; // No discount applied
  const finalTotal = subtotal + shipping + tax - discount;

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="w-5 h-5" />
          {t('checkout.orderSummary')}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Order Items */}
        <div className="space-y-4">
          <h3 className="font-medium text-sm text-gray-700">
            {t('checkout.orderItems')} ({totalItems})
          </h3>
          
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {items.map((item, index) => (
              <div key={`${item.product_id}-${index}`} className="flex gap-3">
                <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                  <Image
                    src={item.product_image || '/placeholder-product.jpg'}
                    alt={item.product_name || 'Product'}
                    fill
                    className="object-cover"
                  />
                  <div className={`absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs bg-blue-600 text-white font-semibold ${item.quantity > 1 ? 'block' : 'hidden'}`}>
                    {item.quantity}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                    {item.product_name || 'Product'}
                  </h4>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-gray-500">
                      {item.quantity} × ${item.price.toLocaleString()}
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      ${(item.price * item.quantity).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Price Breakdown */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{t('checkout.subtotal')}</span>
            <span className="font-medium">${subtotal.toLocaleString()}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 flex items-center gap-1">
              <Truck className="w-3 h-3" />
              {t('checkout.shipping')}
            </span>
            <span className="font-medium">
              {shipping === 0 ? (
                <span className="text-green-600">{t('checkout.free')}</span>
              ) : (
                `$${shipping.toFixed(2)}`
              )}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{t('checkout.tax')}</span>
            <span className="font-medium">${tax.toFixed(2)}</span>
          </div>
          
          {discount > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-green-600 flex items-center gap-1">
                <Tag className="w-3 h-3" />
                {t('checkout.discount')}
              </span>
              <span className="font-medium text-green-600">-${discount.toFixed(2)}</span>
            </div>
          )}
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between items-center">
          <span className="text-lg font-bold text-gray-900">{t('checkout.total')}</span>
          <span className="text-2xl font-bold text-blue-600">${finalTotal.toFixed(2)}</span>
        </div>

        {/* Free Shipping Notice */}
        {shipping > 0 && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              {t('checkout.freeShippingNotice', { amount: (50 - subtotal).toFixed(2) })}
            </p>
          </div>
        )}

        {/* Security Notice */}
        <div className="flex items-center justify-center gap-2 text-sm text-gray-600 pt-4 border-t border-gray-200">
          <Shield className="w-4 h-4" />
          <span>{t('checkout.secureCheckoutNotice')}</span>
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-2">{t('checkout.acceptedPayments')}</p>
          <div className="flex justify-center gap-2">
            {['VISA', 'MC', 'AMEX', 'PP'].map((method) => (
              <div key={method} className="w-8 h-5 bg-gray-200 rounded border flex items-center justify-center">
                <span className="text-xs text-gray-600 font-medium">{method}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Policies */}
        <div className="text-center space-y-2 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
            <span>{t('checkout.returnPolicy')}</span>
            <span>•</span>
            <span>{t('checkout.privacyPolicy')}</span>
          </div>
          <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
            <span>{t('checkout.termsOfService')}</span>
            <span>•</span>
            <span>{t('checkout.customerSupport')}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

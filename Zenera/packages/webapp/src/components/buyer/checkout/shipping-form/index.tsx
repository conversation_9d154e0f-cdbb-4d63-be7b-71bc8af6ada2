"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ContactInformation } from './contact-information';
import { ShippingAddress } from './shipping-address';
import { ShippingMethodSelector } from './shipping-method-selector';

interface ShippingFormProps {
  onSubmit: (data: any) => void;
  locale: string;
}

interface ShippingFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  apartment?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  shippingMethod: string;
  saveAddress: boolean;
}

export function ShippingForm({ onSubmit, locale }: ShippingFormProps) {
  const { t } = useZeneraTranslation('checkout');
  
  const [formData, setFormData] = useState<ShippingFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    apartment: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    shippingMethod: 'standard',
    saveAddress: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof ShippingFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('firstNameRequired');
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('lastNameRequired');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('emailInvalid');
    }
    if (!formData.phone.trim()) {
      newErrors.phone = t('phoneRequired');
    }
    if (!formData.address.trim()) {
      newErrors.address = t('addressRequired');
    }
    if (!formData.city.trim()) {
      newErrors.city = t('cityRequired');
    }
    if (!formData.state.trim()) {
      newErrors.state = t('stateRequired');
    }
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = t('zipCodeRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSubmit(formData);
    } catch (error) {
      console.error('Shipping form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Contact Information */}
      <ContactInformation
        formData={formData}
        errors={errors}
        onInputChange={handleInputChange}
      />

      <Separator />

      {/* Shipping Address */}
      <ShippingAddress
        formData={formData}
        errors={errors}
        onInputChange={handleInputChange}
      />

      <Separator />

      {/* Shipping Method */}
      <ShippingMethodSelector
        selectedMethod={formData.shippingMethod}
        onMethodChange={(methodId) => handleInputChange('shippingMethod', methodId)}
      />

      {/* Save Address Option */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="saveAddress"
          checked={formData.saveAddress}
          onCheckedChange={(checked) => handleInputChange('saveAddress', checked === true)}
        />
        <Label htmlFor="saveAddress" className="text-sm">
          {t('saveAddressForFuture')}
        </Label>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
      >
        {isSubmitting ? t('processing') : t('continueToPayment')}
      </Button>
    </form>
  );
}

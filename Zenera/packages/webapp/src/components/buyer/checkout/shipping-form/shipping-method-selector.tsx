"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Truck, Clock } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShippingMethodSelectorProps {
  selectedMethod: string;
  onMethodChange: (methodId: string) => void;
}

export function ShippingMethodSelector({ selectedMethod, onMethodChange }: ShippingMethodSelectorProps) {
  const { t } = useZeneraTranslation('checkout');

  const shippingMethods = [
    {
      id: 'standard',
      name: t('standardShipping'),
      description: t('standardDescription'),
      price: 9.99,
      duration: '5-7 business days',
      icon: Truck
    },
    {
      id: 'express',
      name: t('expressShipping'),
      description: t('expressDescription'),
      price: 19.99,
      duration: '2-3 business days',
      icon: Clock
    },
    {
      id: 'overnight',
      name: t('overnightShipping'),
      description: t('overnightDescription'),
      price: 39.99,
      duration: 'Next business day',
      icon: Clock
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">{t('shippingMethod')}</h3>
      
      <div className="space-y-3">
        {shippingMethods.map((method) => {
          const Icon = method.icon;
          return (
            <Card 
              key={method.id}
              className={`cursor-pointer transition-colors ${
                selectedMethod === method.id 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'hover:border-gray-300'
              }`}
              onClick={() => onMethodChange(method.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedMethod === method.id 
                        ? 'border-blue-500 bg-blue-500' 
                        : 'border-gray-300'
                    }`}>
                      {selectedMethod === method.id && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                      )}
                    </div>
                    <Icon className="w-5 h-5 text-gray-600" />
                    <div>
                      <h4 className="font-medium">{method.name}</h4>
                      <p className="text-sm text-gray-600">{method.description}</p>
                      <p className="text-xs text-gray-500">{method.duration}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">${method.price}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

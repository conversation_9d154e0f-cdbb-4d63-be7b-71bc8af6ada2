"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  ArrowLeft,
  Shield,
  Building,
  Smartphone
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface PaymentFormProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  locale: string;
}

interface PaymentFormData {
  method: 'card' | 'paypal' | 'bank' | 'wallet';
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardholderName?: string;
  billingAddress?: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  savePaymentMethod: boolean;
}

export function PaymentForm({ onSubmit, onBack, locale }: PaymentFormProps) {
  const { t } = useZeneraTranslation('checkout');
  
  const [formData, setFormData] = useState<PaymentFormData>({
    method: 'card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    billingAddress: {
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
    },
    savePaymentMethod: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const paymentMethods = [
    {
      id: 'card',
      name: t('creditDebitCard'),
      description: t('cardDescription'),
      icon: CreditCard,
      popular: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: t('paypalDescription'),
      icon: Smartphone,
      popular: false
    },
    {
      id: 'bank',
      name: t('bankTransfer'),
      description: t('bankDescription'),
      icon: Building,
      popular: false
    }
  ];

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.startsWith('billingAddress.')) {
      const addressField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        billingAddress: {
          ...prev.billingAddress!,
          [addressField]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\D/g, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const validateCardForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.method === 'card') {
      if (!formData.cardNumber?.replace(/\s/g, '')) {
        newErrors.cardNumber = t('cardNumberRequired');
      } else if (formData.cardNumber.replace(/\s/g, '').length < 13) {
        newErrors.cardNumber = t('cardNumberInvalid');
      }

      if (!formData.expiryDate) {
        newErrors.expiryDate = t('expiryDateRequired');
      } else if (!/^\d{2}\/\d{2}$/.test(formData.expiryDate)) {
        newErrors.expiryDate = t('expiryDateInvalid');
      }

      if (!formData.cvv) {
        newErrors.cvv = t('cvvRequired');
      } else if (formData.cvv.length < 3) {
        newErrors.cvv = t('cvvInvalid');
      }

      if (!formData.cardholderName?.trim()) {
        newErrors.cardholderName = t('cardholderNameRequired');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateCardForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSubmit(formData);
    } catch (error) {
      console.error('Payment form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Method Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('selectPaymentMethod')}</h3>
        
        <div className="space-y-3">
          {paymentMethods.map((method) => {
            const Icon = method.icon;
            return (
              <Card 
                key={method.id}
                className={`cursor-pointer transition-colors ${
                  formData.method === method.id 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'hover:border-gray-300'
                }`}
                onClick={() => handleInputChange('method', method.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        formData.method === method.id 
                          ? 'border-blue-500 bg-blue-500' 
                          : 'border-gray-300'
                      }`}>
                        {formData.method === method.id && (
                          <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                        )}
                      </div>
                      <Icon className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{method.name}</h4>
                          {method.popular && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                              {t('popular')}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{method.description}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      <Separator />

      {/* Card Details Form */}
      {formData.method === 'card' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Shield className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-600">{t('securePayment')}</span>
          </div>

          <div>
            <Label htmlFor="cardNumber">{t('cardNumber')} *</Label>
            <Input
              id="cardNumber"
              value={formData.cardNumber}
              onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
              placeholder="1234 5678 9012 3456"
              maxLength={19}
              className={errors.cardNumber ? 'border-red-500' : ''}
            />
            {errors.cardNumber && (
              <p className="text-sm text-red-600 mt-1">{errors.cardNumber}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expiryDate">{t('expiryDate')} *</Label>
              <Input
                id="expiryDate"
                value={formData.expiryDate}
                onChange={(e) => handleInputChange('expiryDate', formatExpiryDate(e.target.value))}
                placeholder="MM/YY"
                maxLength={5}
                className={errors.expiryDate ? 'border-red-500' : ''}
              />
              {errors.expiryDate && (
                <p className="text-sm text-red-600 mt-1">{errors.expiryDate}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="cvv">{t('cvv')} *</Label>
              <Input
                id="cvv"
                value={formData.cvv}
                onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, ''))}
                placeholder="123"
                maxLength={4}
                className={errors.cvv ? 'border-red-500' : ''}
              />
              {errors.cvv && (
                <p className="text-sm text-red-600 mt-1">{errors.cvv}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="cardholderName">{t('cardholderName')} *</Label>
            <Input
              id="cardholderName"
              value={formData.cardholderName}
              onChange={(e) => handleInputChange('cardholderName', e.target.value)}
              placeholder={t('cardholderNamePlaceholder')}
              className={errors.cardholderName ? 'border-red-500' : ''}
            />
            {errors.cardholderName && (
              <p className="text-sm text-red-600 mt-1">{errors.cardholderName}</p>
            )}
          </div>
        </div>
      )}

      {/* PayPal */}
      {formData.method === 'paypal' && (
        <div className="p-6 bg-blue-50 rounded-lg text-center">
          <div className="text-blue-600 mb-2">
            <Smartphone className="w-8 h-8 mx-auto" />
          </div>
          <h3 className="font-medium mb-2">{t('paypalRedirect')}</h3>
          <p className="text-sm text-gray-600">{t('paypalDescription')}</p>
        </div>
      )}

      {/* Bank Transfer */}
      {formData.method === 'bank' && (
        <div className="p-6 bg-gray-50 rounded-lg">
          <div className="text-gray-600 mb-2">
            <Building className="w-8 h-8 mx-auto" />
          </div>
          <h3 className="font-medium mb-2">{t('bankTransferInstructions')}</h3>
          <p className="text-sm text-gray-600 mb-4">{t('bankTransferDescription')}</p>
          <div className="text-sm space-y-1">
            <p><strong>{t('bankName')}:</strong> Zenera Bank</p>
            <p><strong>{t('accountNumber')}:</strong> **********</p>
            <p><strong>{t('routingNumber')}:</strong> *********</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex-1"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('backToShipping')}
        </Button>

        <Button
          type="submit"
          disabled={isSubmitting}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isSubmitting ? t('processing') : t('reviewOrder')}
        </Button>
      </div>
    </form>
  );
}

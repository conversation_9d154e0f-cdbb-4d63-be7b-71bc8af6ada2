"use client";

import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface AlternativePaymentMethodsProps {
  method: string;
}

export function AlternativePaymentMethods({ method }: AlternativePaymentMethodsProps) {
  const { t } = useZeneraTranslation('checkout');

  if (method === 'paypal') {
    return (
      <div className="space-y-4">
        <h3 className="font-medium mb-2">{t('paypalRedirect')}</h3>
        <p className="text-sm text-gray-600">{t('paypalDescription')}</p>
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            You will be redirected to PayPal to complete your payment securely.
          </p>
        </div>
      </div>
    );
  }

  if (method === 'bank') {
    return (
      <div className="space-y-4">
        <h3 className="font-medium mb-2">{t('bankTransferInstructions')}</h3>
        <p className="text-sm text-gray-600 mb-4">{t('bankTransferDescription')}</p>
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm space-y-1">
            <p><strong>{t('bankName')}:</strong> Zenera Bank</p>
            <p><strong>{t('accountNumber')}:</strong> **********</p>
            <p><strong>{t('routingNumber')}:</strong> *********</p>
          </div>
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-xs text-yellow-800">
              Please include your order number in the transfer reference.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

"use client";

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Shield } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CardPaymentFormProps {
  formData: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
  };
  errors: Record<string, string>;
  onInputChange: (field: string, value: string) => void;
}

export function CardPaymentForm({ formData, errors, onInputChange }: CardPaymentFormProps) {
  const { t } = useZeneraTranslation('checkout');

  const formatCardNumber = (value: string): string => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string): string => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Shield className="w-5 h-5 text-green-600" />
        <span className="text-sm text-gray-600">{t('securePayment')}</span>
      </div>

      <div>
        <Label htmlFor="cardNumber">{t('cardNumber')} *</Label>
        <Input
          id="cardNumber"
          value={formData.cardNumber}
          onChange={(e) => onInputChange('cardNumber', formatCardNumber(e.target.value))}
          placeholder="1234 5678 9012 3456"
          className={errors.cardNumber ? 'border-red-500' : ''}
          maxLength={19}
        />
        {errors.cardNumber && (
          <p className="text-sm text-red-600 mt-1">{errors.cardNumber}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expiryDate">{t('expiryDate')} *</Label>
          <Input
            id="expiryDate"
            value={formData.expiryDate}
            onChange={(e) => onInputChange('expiryDate', formatExpiryDate(e.target.value))}
            placeholder="MM/YY"
            className={errors.expiryDate ? 'border-red-500' : ''}
            maxLength={5}
          />
          {errors.expiryDate && (
            <p className="text-sm text-red-600 mt-1">{errors.expiryDate}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="cvv">{t('cvv')} *</Label>
          <Input
            id="cvv"
            value={formData.cvv}
            onChange={(e) => onInputChange('cvv', e.target.value.replace(/\D/g, ''))}
            placeholder="123"
            className={errors.cvv ? 'border-red-500' : ''}
            maxLength={4}
          />
          {errors.cvv && (
            <p className="text-sm text-red-600 mt-1">{errors.cvv}</p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="cardholderName">{t('cardholderName')} *</Label>
        <Input
          id="cardholderName"
          value={formData.cardholderName}
          onChange={(e) => onInputChange('cardholderName', e.target.value)}
          placeholder={t('cardholderNamePlaceholder')}
          className={errors.cardholderName ? 'border-red-500' : ''}
        />
        {errors.cardholderName && (
          <p className="text-sm text-red-600 mt-1">{errors.cardholderName}</p>
        )}
      </div>
    </div>
  );
}

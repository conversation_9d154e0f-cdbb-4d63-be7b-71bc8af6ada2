"use client";

import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Smartphone, Building } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onMethodChange: (method: string) => void;
}

export function PaymentMethodSelector({ selectedMethod, onMethodChange }: PaymentMethodSelectorProps) {
  const { t } = useZeneraTranslation('checkout');

  const paymentMethods = [
    {
      id: 'card',
      name: t('creditDebitCard'),
      description: t('cardDescription'),
      icon: CreditCard,
      popular: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: t('paypalDescription'),
      icon: Smartphone,
      popular: false
    },
    {
      id: 'bank',
      name: t('bankTransfer'),
      description: t('bankDescription'),
      icon: Building,
      popular: false
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">{t('selectPaymentMethod')}</h3>
      
      <div className="space-y-3">
        {paymentMethods.map((method) => {
          const Icon = method.icon;
          return (
            <Card 
              key={method.id}
              className={`cursor-pointer transition-colors ${
                selectedMethod === method.id 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'hover:border-gray-300'
              }`}
              onClick={() => onMethodChange(method.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedMethod === method.id 
                        ? 'border-blue-500 bg-blue-500' 
                        : 'border-gray-300'
                    }`}>
                      {selectedMethod === method.id && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                      )}
                    </div>
                    <Icon className="w-5 h-5 text-gray-600" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{method.name}</h4>
                        {method.popular && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                            {t('popular')}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

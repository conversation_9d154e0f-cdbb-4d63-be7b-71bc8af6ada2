"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ShoppingCart, ArrowLeft } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CartHeaderProps {
  locale: string;
  totalItems: number;
}

export function CartHeader({ locale, totalItems }: CartHeaderProps) {
  const { t } = useZeneraTranslation('cart');

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-blue-100 rounded-xl">
          <ShoppingCart className="w-8 h-8 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            {t('title')}
          </h1>
          <p className="text-gray-600 mt-1">
            {t('itemsCount', { count: totalItems })}
          </p>
        </div>
      </div>

      <Link href={`/${locale}/products`}>
        <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
          <ArrowLeft className="w-4 h-4" />
          {t('continueShopping')}
        </Button>
      </Link>
    </div>
  );
}

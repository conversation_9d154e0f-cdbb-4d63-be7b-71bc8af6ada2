"use client";

import { useState } from 'react';
import Link from 'next/link';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingCart, 
  ArrowLeft, 
  Trash2, 
  Plus, 
  Minus,
  ShoppingBag,
  CreditCard,
  Shield,
  Truck
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { CartItemCard } from './cart-item-card';
import { CartSummary } from './cart-summary';
import { EmptyCart } from './empty-cart';
import { RecommendedProducts } from './recommended-products';

interface CartPageProps {
  locale: string;
}

export function CartPage({ locale }: CartPageProps) {
  const { t } = useZeneraTranslation('cart');
  const { items, getTotalPrice, getTotalItems } = useCartStore();
  const [isLoading, setIsLoading] = useState(false);

  const breadcrumbItems = [
    { label: t('home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/cart` },
  ];

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Breadcrumb */}
        <div className="container mx-auto px-4 pt-6">
          <SimpleBreadcrumb items={breadcrumbItems} />
        </div>

        <EmptyCart locale={locale} />
        <RecommendedProducts locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Header */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <ShoppingCart className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('title')}
              </h1>
              <p className="text-gray-600 mt-1">
                {t('itemsCount', { count: totalItems })}
              </p>
            </div>
          </div>

          <Link href={`/${locale}/products`}>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              {t('continueShopping')}
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingBag className="w-5 h-5" />
                  {t('yourItems')} ({totalItems})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {items.map((item, index) => (
                  <div key={`${item.product_id}-${index}`}>
                    <CartItemCard item={item} locale={locale} />
                    {index < items.length - 1 && <Separator className="my-4" />}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Security & Shipping Info */}
            <Card>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Shield className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{t('secureCheckout')}</h3>
                      <p className="text-sm text-gray-600">{t('secureDescription')}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Truck className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{t('freeShipping')}</h3>
                      <p className="text-sm text-gray-600">{t('freeShippingDescription')}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <CreditCard className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{t('multiplePayment')}</h3>
                      <p className="text-sm text-gray-600">{t('paymentDescription')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <CartSummary 
                totalPrice={totalPrice}
                totalItems={totalItems}
                locale={locale}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>

        {/* Recommended Products */}
        <div className="mt-12">
          <RecommendedProducts locale={locale} />
        </div>
      </div>
    </div>
  );
}

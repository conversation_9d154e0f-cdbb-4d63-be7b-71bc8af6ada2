"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { CartHeader } from './cart-header';
import { CartItemsList } from './cart-items-list';
import { CartSecurityInfo } from './cart-security-info';
import { CartSummary } from './cart-summary';
import { EmptyCart } from './empty-cart';
import { RecommendedProducts } from './recommended-products';

interface CartPageProps {
  locale: string;
}

export function CartPage({ locale }: CartPageProps) {
  const { t } = useZeneraTranslation('cart');
  const { items, getTotalPrice, getTotalItems } = useCartStore();
  const [isLoading, setIsLoading] = useState(false);

  const breadcrumbItems = [
    { label: t('home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/cart` },
  ];

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Breadcrumb */}
        <div className="container mx-auto px-4 pt-6">
          <SimpleBreadcrumb items={breadcrumbItems} />
        </div>

        <EmptyCart locale={locale} />
        <RecommendedProducts locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Header */}
      <div className="container mx-auto px-4 py-8">
        <CartHeader locale={locale} totalItems={totalItems} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            <CartItemsList
              items={items}
              totalItems={totalItems}
              locale={locale}
            />

            {/* Security & Shipping Info */}
            <CartSecurityInfo />
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <CartSummary 
                totalPrice={totalPrice}
                totalItems={totalItems}
                locale={locale}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>

        {/* Recommended Products */}
        <div className="mt-12">
          <RecommendedProducts locale={locale} />
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Star, 
  ShoppingCart, 
  Heart,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';
import type { Product } from '@zenera/sharing';

interface RecommendedProductsProps {
  locale: string;
}

export function RecommendedProducts({ locale }: RecommendedProductsProps) {
  const { t } = useZeneraTranslation('cart');
  const { addItem } = useCartStore();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock recommended products - in real app, this would be based on cart items, user history, etc.
    const fetchRecommendedProducts = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockProducts: Product[] = [
          {
            id: 'rec-1',
            name: 'Wireless Bluetooth Headphones',
            slug: 'wireless-bluetooth-headphones',
            description: 'Premium sound quality with noise cancellation',
            category_id: 'electronics',
            brand: 'AudioTech',
            tags: ['recommended', 'popular'],
            images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500'],
            is_active: true,
            attributes: [],
            base_price: 129.99,
            compare_at_price: 159.99,
            avg_rating: 4.6,
            review_count: 324,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            id: 'rec-2',
            name: 'Smart Fitness Watch',
            slug: 'smart-fitness-watch',
            description: 'Track your health and fitness goals',
            category_id: 'electronics',
            brand: 'FitTech',
            tags: ['recommended', 'trending'],
            images: ['https://images.unsplash.com/photo-*************-37898b6baf30?w=500'],
            is_active: true,
            attributes: [],
            base_price: 199.99,
            compare_at_price: 249.99,
            avg_rating: 4.4,
            review_count: 156,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            id: 'rec-3',
            name: 'Portable Power Bank',
            slug: 'portable-power-bank',
            description: 'Never run out of battery on the go',
            category_id: 'electronics',
            brand: 'PowerTech',
            tags: ['recommended', 'essential'],
            images: ['https://images.unsplash.com/photo-*************-d0ae3d1b9b9e?w=500'],
            is_active: true,
            attributes: [],
            base_price: 39.99,
            compare_at_price: 49.99,
            avg_rating: 4.3,
            review_count: 89,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            id: 'rec-4',
            name: 'USB-C Hub Adapter',
            slug: 'usb-c-hub-adapter',
            description: 'Expand your connectivity options',
            category_id: 'electronics',
            brand: 'ConnectTech',
            tags: ['recommended', 'useful'],
            images: ['https://images.unsplash.com/photo-*************-8f3296236761?w=500'],
            is_active: true,
            attributes: [],
            base_price: 59.99,
            avg_rating: 4.5,
            review_count: 67,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
        
        setProducts(mockProducts);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecommendedProducts();
  }, []);

  const handleAddToCart = (product: Product) => {
    addItem(product);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Sparkles className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">{t('recommendedProducts')}</h2>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Sparkles className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">{t('recommendedProducts')}</h2>
        </div>
        
        <Link href={`/${locale}/products`}>
          <Button variant="outline" className="flex items-center gap-2">
            {t('viewAllProducts')}
            <ArrowRight className="w-4 h-4" />
          </Button>
        </Link>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => {
          const discountPercentage = product.compare_at_price && product.base_price
            ? Math.round(((product.compare_at_price - product.base_price) / product.compare_at_price) * 100)
            : 0;

          return (
            <Card key={product.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-0">
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden rounded-t-lg">
                  <Link href={`/${locale}/products/${product.slug}`}>
                    <Image
                      src={product.images[0] || '/placeholder-product.jpg'}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </Link>
                  
                  {/* Discount Badge */}
                  <div className={`absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold ${discountPercentage > 0 ? 'block' : 'hidden'}`}>
                    -{discountPercentage}%
                  </div>

                  {/* Quick Actions */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                    >
                      <Heart className="w-4 h-4 text-gray-600" />
                    </Button>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <Link href={`/${locale}/products/${product.slug}`}>
                    <h3 className="font-medium text-gray-900 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors">
                      {product.name}
                    </h3>
                  </Link>

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-3">
                    <div className="flex items-center">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "w-3 h-3",
                            i < Math.floor(product.avg_rating) 
                              ? "text-yellow-400 fill-current" 
                              : "text-gray-300"
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">
                      ({product.review_count})
                    </span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-lg font-bold text-blue-600">
                      ${product.base_price?.toLocaleString()}
                    </span>
                    {product.compare_at_price && product.compare_at_price > (product.base_price || 0) && (
                      <span className="text-sm text-gray-500 line-through">
                        ${product.compare_at_price.toLocaleString()}
                      </span>
                    )}
                  </div>

                  {/* Add to Cart Button */}
                  <Button
                    onClick={() => handleAddToCart(product)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    size="sm"
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    {t('addToCart')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

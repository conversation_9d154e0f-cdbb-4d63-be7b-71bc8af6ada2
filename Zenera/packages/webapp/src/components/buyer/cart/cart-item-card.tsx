"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2, 
  Plus, 
  Minus, 
  Heart,
  ExternalLink
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';
import type { CartItem } from '@zenera/sharing';

interface CartItemCardProps {
  item: CartItem;
  locale: string;
}

export function CartItemCard({ item, locale }: CartItemCardProps) {
  const { t } = useZeneraTranslation('cart');
  const { updateQuantity, removeItem } = useCartStore();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setIsUpdating(true);
    try {
      updateQuantity(item.product_id, newQuantity);
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 300));
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = () => {
    removeItem(item.product_id);
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    // TODO: Add to wishlist API call
  };

  const itemTotal = item.price * item.quantity;

  return (
    <div className="flex gap-4 p-4 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
      {/* Product Image */}
      <div className="flex-shrink-0">
        <Link href={`/${locale}/products/${item.product_slug}`}>
          <div className="relative w-20 h-20 md:w-24 md:h-24 rounded-lg overflow-hidden bg-gray-100 hover:opacity-80 transition-opacity">
            <Image
              src={item.product_image || '/placeholder-product.jpg'}
              alt={item.product_name || 'Product'}
              fill
              className="object-cover"
            />
          </div>
        </Link>
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1 min-w-0">
            <Link 
              href={`/${locale}/products/${item.product_slug}`}
              className="group"
            >
              <h3 className="font-medium text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                {item.product_name || 'Product'}
              </h3>
            </Link>
            
            {!item.is_available && (
              <Badge variant="destructive" className="mt-1">
                {t('cart.outOfStock')}
              </Badge>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleWishlist}
              className="h-8 w-8 p-0"
            >
              <Heart className={cn(
                "w-4 h-4",
                isWishlisted ? "text-red-500 fill-current" : "text-gray-400"
              )} />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemove}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Price and Quantity */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Quantity Controls */}
            <div className="flex items-center border border-gray-200 rounded-lg">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity - 1)}
                disabled={item.quantity <= 1 || isUpdating || !item.is_available}
                className="h-8 w-8 p-0 rounded-r-none"
              >
                <Minus className="w-3 h-3" />
              </Button>
              
              <div className="px-3 py-1 text-sm font-medium min-w-[40px] text-center border-x border-gray-200">
                {isUpdating ? '...' : item.quantity}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity + 1)}
                disabled={isUpdating || !item.is_available}
                className="h-8 w-8 p-0 rounded-l-none"
              >
                <Plus className="w-3 h-3" />
              </Button>
            </div>

            {/* Unit Price */}
            <div className="text-sm text-gray-600">
              ${item.price.toLocaleString()} {t('cart.each')}
            </div>
          </div>

          {/* Total Price */}
          <div className="text-right">
            <div className="text-lg font-bold text-gray-900">
              ${itemTotal.toLocaleString()}
            </div>
            {item.quantity > 1 && (
              <div className="text-xs text-gray-500">
                {item.quantity} × ${item.price.toLocaleString()}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center gap-4 mt-3 pt-3 border-t border-gray-100">
          <Link href={`/${locale}/products/${item.product_slug}`}>
            <Button variant="ghost" size="sm" className="text-xs h-7">
              <ExternalLink className="w-3 h-3 mr-1" />
              {t('cart.viewProduct')}
            </Button>
          </Link>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleWishlist}
            className="text-xs h-7"
          >
            <Heart className="w-3 h-3 mr-1" />
            {isWishlisted ? t('cart.removeFromWishlist') : t('cart.saveForLater')}
          </Button>
        </div>
      </div>
    </div>
  );
}

"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  ShoppingCart, 
  ArrowRight, 
  Heart,
  Search,
  Sparkles
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface EmptyCartProps {
  locale: string;
}

export function EmptyCart({ locale }: EmptyCartProps) {
  const { t } = useZeneraTranslation('cart');

  const quickActions = [
    {
      icon: Search,
      title: t('empty.browseProducts'),
      description: t('empty.browseDescription'),
      href: `/${locale}/products`,
      color: 'blue'
    },
    {
      icon: Sparkles,
      title: t('empty.featuredProducts'),
      description: t('empty.featuredDescription'),
      href: `/${locale}#featured`,
      color: 'purple'
    },
    {
      icon: Heart,
      title: t('empty.wishlist'),
      description: t('empty.wishlistDescription'),
      href: `/${locale}/wishlist`,
      color: 'red'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 sm:py-16">
      <Card className="max-w-2xl mx-auto bg-white/80 backdrop-blur-sm border-0 shadow-xl">
        <CardContent className="p-6 sm:p-12 text-center">
          {/* Empty Cart Icon */}
          <div className="relative mb-6 sm:mb-8">
            <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
              <ShoppingCart className="w-12 h-12 sm:w-16 sm:h-16 text-blue-600" />
            </div>
            <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs sm:text-sm font-bold">0</span>
            </div>
          </div>

          {/* Empty State Content */}
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            {t('empty.title')}
          </h2>

          <p className="text-gray-600 text-base sm:text-lg mb-8 max-w-md mx-auto">
            {t('empty.description')}
          </p>

          {/* Primary CTA */}
          <Link href={`/${locale}/products`}>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 text-base sm:text-lg font-medium mb-8 w-full sm:w-auto">
              {t('empty.startShopping')}
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </Link>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              const colorClasses = {
                blue: 'bg-blue-50 text-blue-600 border-blue-200',
                purple: 'bg-purple-50 text-purple-600 border-purple-200',
                red: 'bg-red-50 text-red-600 border-red-200'
              };
              
              return (
                <Link key={index} href={action.href}>
                  <Card className="h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer border border-gray-200 hover:border-gray-300">
                    <CardContent className="p-6 text-center">
                      <div className={`w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center ${colorClasses[action.color as keyof typeof colorClasses]}`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      <h3 className="font-medium text-gray-900 mb-2">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {action.description}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>

          {/* Help Text */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              {t('empty.helpText')} {' '}
              <Link href={`/${locale}/support`} className="text-blue-600 hover:text-blue-700 font-medium">
                {t('empty.contactSupport')}
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

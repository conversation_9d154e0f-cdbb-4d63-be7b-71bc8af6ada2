"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Shield, Truck, CreditCard } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

export function CartSecurityInfo() {
  const { t } = useZeneraTranslation('cart');

  const securityFeatures = [
    {
      icon: Shield,
      title: t('secureCheckout'),
      description: t('secureDescription'),
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600'
    },
    {
      icon: Truck,
      title: t('freeShipping'),
      description: t('freeShippingDescription'),
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      icon: CreditCard,
      title: t('multiplePayment'),
      description: t('paymentDescription'),
      bgColor: 'bg-purple-100',
      iconColor: 'text-purple-600'
    }
  ];

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {securityFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="flex items-center gap-3">
                <div className={`p-2 ${feature.bgColor} rounded-lg`}>
                  <Icon className={`w-5 h-5 ${feature.iconColor}`} />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

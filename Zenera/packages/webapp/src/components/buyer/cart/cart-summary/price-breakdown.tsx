"use client";

import { Truck } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface PriceBreakdownProps {
  subtotal: number;
  totalItems: number;
  shipping: number;
  tax: number;
  discount: number;
}

export function PriceBreakdown({ subtotal, totalItems, shipping, tax, discount }: PriceBreakdownProps) {
  const { t } = useZeneraTranslation('cart');

  return (
    <div className="space-y-3">
      <div className="flex justify-between text-sm">
        <span className="text-gray-600">{t('subtotal')} ({totalItems} {t('items')})</span>
        <span className="font-medium">${subtotal.toLocaleString()}</span>
      </div>

      <div className="flex justify-between text-sm">
        <span className="text-gray-600 flex items-center gap-1">
          <Truck className="w-3 h-3" />
          {t('shipping')}
        </span>
        <span className="font-medium">
          {shipping === 0 ? (
            <span className="text-green-600">{t('free')}</span>
          ) : (
            `$${shipping.toFixed(2)}`
          )}
        </span>
      </div>

      <div className="flex justify-between text-sm">
        <span className="text-gray-600">{t('tax')}</span>
        <span className="font-medium">${tax.toFixed(2)}</span>
      </div>

      {discount > 0 && (
        <div className="flex justify-between text-sm">
          <span className="text-green-600">{t('discount')}</span>
          <span className="font-medium text-green-600">-${discount.toFixed(2)}</span>
        </div>
      )}
    </div>
  );
}

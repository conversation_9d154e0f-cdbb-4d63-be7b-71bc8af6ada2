"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tag, Percent } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CouponSectionProps {
  appliedCoupon: {
    code: string;
    discount: number;
    type: 'percentage' | 'fixed';
  } | null;
  onApplyCoupon: (code: string) => void;
  onRemoveCoupon: () => void;
  couponError: string;
}

export function CouponSection({ appliedCoupon, onApplyCoupon, onRemoveCoupon, couponError }: CouponSectionProps) {
  const { t } = useZeneraTranslation('cart');
  const [couponCode, setCouponCode] = useState('');

  const handleApply = () => {
    onApplyCoupon(couponCode);
    setCouponCode('');
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Tag className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium">{t('couponCode')}</span>
      </div>
      
      {appliedCoupon ? (
        <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Percent className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">
              {appliedCoupon.code}
            </span>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {appliedCoupon.type === 'percentage' 
                ? `${appliedCoupon.discount}% off`
                : `$${appliedCoupon.discount} off`
              }
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemoveCoupon}
            className="text-green-700 hover:text-green-800 h-6 px-2"
          >
            {t('remove', 'Remove')}
          </Button>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              placeholder={t('enterCouponCode')}
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              className="flex-1"
            />
            <Button
              variant="outline"
              onClick={handleApply}
              disabled={!couponCode.trim()}
            >
              {t('apply')}
            </Button>
          </div>
          {couponError && (
            <p className="text-sm text-red-600">{couponError}</p>
          )}
        </div>
      )}
    </div>
  );
}

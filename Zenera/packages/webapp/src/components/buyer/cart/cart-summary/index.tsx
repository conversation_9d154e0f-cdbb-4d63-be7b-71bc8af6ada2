"use client";

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CreditCard } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { CouponSection } from './coupon-section';
import { PriceBreakdown } from './price-breakdown';
import { CheckoutSection } from './checkout-section';

interface CartSummaryProps {
  totalPrice: number;
  totalItems: number;
  locale: string;
  isLoading?: boolean;
}

export function CartSummary({ totalPrice, totalItems, locale, isLoading }: CartSummaryProps) {
  const { t } = useZeneraTranslation('cart');
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    type: 'percentage' | 'fixed';
  } | null>(null);
  const [couponError, setCouponError] = useState('');

  // Mock calculations
  const subtotal = totalPrice;
  const shipping = subtotal >= 50 ? 0 : 9.99; // Free shipping over $50
  const tax = subtotal * 0.1; // 10% tax
  const discount = appliedCoupon 
    ? appliedCoupon.type === 'percentage' 
      ? subtotal * (appliedCoupon.discount / 100)
      : appliedCoupon.discount
    : 0;
  const finalTotal = subtotal + shipping + tax - discount;

  const handleApplyCoupon = (code: string) => {
    setCouponError('');
    
    // Mock coupon validation
    const mockCoupons = {
      'SAVE10': { discount: 10, type: 'percentage' as const },
      'WELCOME20': { discount: 20, type: 'percentage' as const },
      'FLAT5': { discount: 5, type: 'fixed' as const },
    };

    const coupon = mockCoupons[code.toUpperCase() as keyof typeof mockCoupons];
    
    if (coupon) {
      setAppliedCoupon({
        code: code.toUpperCase(),
        ...coupon
      });
    } else {
      setCouponError(t('invalidCoupon'));
    }
  };

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          {t('orderSummary')}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Coupon Code */}
        <CouponSection
          appliedCoupon={appliedCoupon}
          onApplyCoupon={handleApplyCoupon}
          onRemoveCoupon={handleRemoveCoupon}
          couponError={couponError}
        />

        <Separator />

        {/* Price Breakdown */}
        <PriceBreakdown
          subtotal={subtotal}
          totalItems={totalItems}
          shipping={shipping}
          tax={tax}
          discount={discount}
        />

        <Separator />

        {/* Checkout Section */}
        <CheckoutSection
          finalTotal={finalTotal}
          subtotal={subtotal}
          shipping={shipping}
          locale={locale}
          isLoading={isLoading}
        />
      </CardContent>
    </Card>
  );
}

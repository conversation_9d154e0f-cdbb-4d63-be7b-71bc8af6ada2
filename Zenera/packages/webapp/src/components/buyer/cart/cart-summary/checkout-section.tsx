"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Shield, ArrowRight } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CheckoutSectionProps {
  finalTotal: number;
  subtotal: number;
  shipping: number;
  locale: string;
  isLoading?: boolean;
}

export function CheckoutSection({ finalTotal, subtotal, shipping, locale, isLoading }: CheckoutSectionProps) {
  const { t } = useZeneraTranslation('cart');

  return (
    <div className="space-y-4">
      {/* Total */}
      <div className="flex justify-between items-center">
        <span className="text-lg font-bold text-gray-900">{t('total')}</span>
        <span className="text-2xl font-bold text-blue-600">${finalTotal.toFixed(2)}</span>
      </div>

      {/* Free Shipping Notice */}
      {shipping > 0 && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            {t('freeShippingNotice', { amount: (50 - subtotal).toFixed(2) })}
          </p>
        </div>
      )}

      {/* Checkout Button */}
      <Link href={`/${locale}/checkout`}>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium"
          disabled={isLoading}
        >
          {isLoading ? (
            t('processing')
          ) : (
            <>
              {t('proceedToCheckout')}
              <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </Button>
      </Link>

      {/* Security Notice */}
      <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
        <Shield className="w-4 h-4" />
        <span>{t('secureCheckoutNotice')}</span>
      </div>

      {/* Payment Methods */}
      <div className="text-center">
        <p className="text-xs text-gray-500 mb-2">{t('acceptedPayments')}</p>
        <div className="flex justify-center gap-2">
          {['visa', 'mastercard', 'paypal', 'apple-pay'].map((method) => (
            <div key={method} className="w-8 h-5 bg-gray-200 rounded border flex items-center justify-center">
              <span className="text-xs text-gray-600">{method.slice(0, 2).toUpperCase()}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

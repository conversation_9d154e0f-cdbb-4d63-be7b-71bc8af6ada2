"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Tag, 
  Truck, 
  Shield,
  ArrowRight,
  Percent
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface CartSummaryProps {
  totalPrice: number;
  totalItems: number;
  locale: string;
  isLoading?: boolean;
}

export function CartSummary({ totalPrice, totalItems, locale, isLoading }: CartSummaryProps) {
  const { t } = useZeneraTranslation('cart');
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    type: 'percentage' | 'fixed';
  } | null>(null);
  const [couponError, setCouponError] = useState('');

  // Mock calculations
  const subtotal = totalPrice;
  const shipping = subtotal >= 50 ? 0 : 9.99; // Free shipping over $50
  const tax = subtotal * 0.1; // 10% tax
  const discount = appliedCoupon 
    ? appliedCoupon.type === 'percentage' 
      ? subtotal * (appliedCoupon.discount / 100)
      : appliedCoupon.discount
    : 0;
  const finalTotal = subtotal + shipping + tax - discount;

  const handleApplyCoupon = () => {
    setCouponError('');
    
    // Mock coupon validation
    const mockCoupons = {
      'SAVE10': { discount: 10, type: 'percentage' as const },
      'WELCOME20': { discount: 20, type: 'percentage' as const },
      'FLAT5': { discount: 5, type: 'fixed' as const },
    };

    const coupon = mockCoupons[couponCode.toUpperCase() as keyof typeof mockCoupons];
    
    if (coupon) {
      setAppliedCoupon({
        code: couponCode.toUpperCase(),
        ...coupon
      });
      setCouponCode('');
    } else {
      setCouponError(t('invalidCoupon'));
    }
  };

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          {t('orderSummary')}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Coupon Code */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Tag className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium">{t('couponCode')}</span>
          </div>
          
          {appliedCoupon ? (
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Percent className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  {appliedCoupon.code}
                </span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {appliedCoupon.type === 'percentage' 
                    ? `${appliedCoupon.discount}% off`
                    : `$${appliedCoupon.discount} off`
                  }
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveCoupon}
                className="text-green-700 hover:text-green-800 h-6 px-2"
              >
                {t('remove', 'Remove')}
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder={t('enterCouponCode')}
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={handleApplyCoupon}
                  disabled={!couponCode.trim()}
                >
                  {t('apply')}
                </Button>
              </div>
              {couponError && (
                <p className="text-sm text-red-600">{couponError}</p>
              )}
            </div>
          )}
        </div>

        <Separator />

        {/* Price Breakdown */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{t('subtotal')} ({totalItems} {t('items')})</span>
            <span className="font-medium">${subtotal.toLocaleString()}</span>
          </div>

          <div className="flex justify-between text-sm">
            <span className="text-gray-600 flex items-center gap-1">
              <Truck className="w-3 h-3" />
              {t('shipping')}
            </span>
            <span className="font-medium">
              {shipping === 0 ? (
                <span className="text-green-600">{t('free')}</span>
              ) : (
                `$${shipping.toFixed(2)}`
              )}
            </span>
          </div>

          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{t('tax')}</span>
            <span className="font-medium">${tax.toFixed(2)}</span>
          </div>

          {discount > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-green-600">{t('discount')}</span>
              <span className="font-medium text-green-600">-${discount.toFixed(2)}</span>
            </div>
          )}
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between items-center">
          <span className="text-lg font-bold text-gray-900">{t('total')}</span>
          <span className="text-2xl font-bold text-blue-600">${finalTotal.toFixed(2)}</span>
        </div>

        {/* Free Shipping Notice */}
        {shipping > 0 && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              {t('freeShippingNotice', { amount: (50 - subtotal).toFixed(2) })}
            </p>
          </div>
        )}

        {/* Checkout Button */}
        <Link href={`/${locale}/checkout`}>
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium"
            disabled={isLoading}
          >
            {isLoading ? (
              t('processing')
            ) : (
              <>
                {t('proceedToCheckout')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </>
            )}
          </Button>
        </Link>

        {/* Security Notice */}
        <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
          <Shield className="w-4 h-4" />
          <span>{t('secureCheckoutNotice')}</span>
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-2">{t('acceptedPayments')}</p>
          <div className="flex justify-center gap-2">
            {['visa', 'mastercard', 'paypal', 'apple-pay'].map((method) => (
              <div key={method} className="w-8 h-5 bg-gray-200 rounded border flex items-center justify-center">
                <span className="text-xs text-gray-600">{method.slice(0, 2).toUpperCase()}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

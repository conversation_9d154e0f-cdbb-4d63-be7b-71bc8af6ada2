"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ShoppingBag } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { CartItemCard } from './cart-item-card';
import type { CartItem } from '@zenera/sharing';

interface CartItemsListProps {
  items: CartItem[];
  totalItems: number;
  locale: string;
}

export function CartItemsList({ items, totalItems, locale }: CartItemsListProps) {
  const { t } = useZeneraTranslation('cart');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="w-5 h-5" />
          {t('yourItems')} ({totalItems})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((item, index) => (
          <div key={`${item.product_id}-${index}`}>
            <CartItemCard item={item} locale={locale} />
            {index < items.length - 1 && <Separator className="my-4" />}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

"use client";

import { useState } from 'react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Mail, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface NewsletterSectionProps {
  locale: string;
}

export function NewsletterSection({ locale }: NewsletterSectionProps) {
  const { t } = useZeneraTranslation('home');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      setStatus('error');
      setMessage(t('newsletter.invalidEmail'));
      return;
    }

    setIsLoading(true);
    setStatus('idle');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock success response
      setStatus('success');
      setMessage(t('newsletter.subscribeSuccess'));
      setEmail('');
    } catch (error) {
      setStatus('error');
      setMessage(t('newsletter.subscribeError'));
    } finally {
      setIsLoading(false);
    }
  };

  const benefits = [
    {
      icon: '🎯',
      title: t('newsletter.benefits.exclusive.title'),
      description: t('newsletter.benefits.exclusive.description')
    },
    {
      icon: '⚡',
      title: t('newsletter.benefits.early.title'),
      description: t('newsletter.benefits.early.description')
    },
    {
      icon: '💰',
      title: t('newsletter.benefits.deals.title'),
      description: t('newsletter.benefits.deals.description')
    },
    {
      icon: '📱',
      title: t('newsletter.benefits.updates.title'),
      description: t('newsletter.benefits.updates.description')
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20">
        <div className="absolute inset-0 bg-[url('/images/newsletter-pattern.svg')] opacity-10"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-yellow-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-purple-400/20 rounded-full blur-xl animate-pulse delay-2000"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="secondary" className="bg-white/20 text-white border-0">
                {t('newsletter.badge')}
              </Badge>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                {t('newsletter.title')}
              </h2>
              <p className="text-xl text-blue-100 max-w-lg">
                {t('newsletter.description')}
              </p>
            </div>

            {/* Newsletter Form */}
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1">
                      <Input
                        type="email"
                        placeholder={t('newsletter.emailPlaceholder')}
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="bg-white/20 border-white/30 text-white placeholder-white/70 focus:bg-white/30"
                        disabled={isLoading}
                      />
                    </div>
                    <Button
                      type="submit"
                      size="lg"
                      disabled={isLoading}
                      className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white border-0 px-8 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {isLoading ? (
                        <div className="flex items-center">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                          {t('newsletter.subscribing')}
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2" />
                          {t('newsletter.subscribe')}
                        </div>
                      )}
                    </Button>
                  </div>

                  {/* Status Message */}
                  {status !== 'idle' && (
                    <div className={`flex items-center space-x-2 text-sm ${
                      status === 'success' ? 'text-green-200' : 'text-red-200'
                    }`}>
                      {status === 'success' ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      <span>{message}</span>
                    </div>
                  )}
                </form>

                <p className="text-xs text-white/70 mt-4">
                  {t('newsletter.privacy')}
                </p>
              </CardContent>
            </Card>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-400">25K+</div>
                <div className="text-sm text-blue-200">{t('newsletter.stats.subscribers')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-400">Weekly</div>
                <div className="text-sm text-blue-200">{t('newsletter.stats.frequency')}</div>
              </div>
            </div>
          </div>

          {/* Right Content - Benefits */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold mb-6">{t('newsletter.benefitsTitle')}</h3>
            <div className="grid gap-4">
              {benefits.map((benefit, index) => (
                <Card
                  key={index}
                  className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
                >
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      <div className="text-2xl">{benefit.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-white mb-1">
                          {benefit.title}
                        </h4>
                        <p className="text-sm text-blue-100">
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Social Proof */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="flex -space-x-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div
                      key={i}
                      className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full border-2 border-white flex items-center justify-center text-xs font-bold text-white"
                    >
                      {String.fromCharCode(65 + i)}
                    </div>
                  ))}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-white">
                    <span className="font-semibold">2,847</span> {t('newsletter.socialProof.joined')}
                  </p>
                  <p className="text-xs text-blue-200">{t('newsletter.socialProof.thisWeek')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

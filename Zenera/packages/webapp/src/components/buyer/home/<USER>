"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Star, Heart, ShoppingCart, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { useProductsStore } from '@/stores/products-store';
import { useCartStore } from '@/stores/cart-store';
// Temporarily using local interface for home page
interface HomeProduct {
  _id?: string;
  id?: string;
  title: string;
  name?: string;
  slug?: string;
  description: string;
  price: number;
  base_price?: number;
  originalPrice?: number;
  compare_at_price?: number;
  images: string[];
  category: string;
  category_id?: string;
  brand?: string;
  rating: number;
  avg_rating?: number;
  reviewCount: number;
  review_count?: number;
  inStock?: boolean;
  is_active?: boolean;
  stockQuantity?: number;
  seller?: {
    _id: string;
    name: string;
    rating: number;
    totalSales: number;
  };
  tags: string[];
  attributes?: any[];
  createdAt: Date;
  created_at?: Date;
  updatedAt: Date;
  updated_at?: Date;
}

export function FeaturedProducts() {
  const { t } = useZeneraTranslation('home');
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const [featuredProducts, setFeaturedProducts] = useState<HomeProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { addItem } = useCartStore();
  const productsPerPage = 4;

  // Mock featured products data - replace with actual API call
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockProducts: HomeProduct[] = [
          {
            _id: '1',
            title: 'Premium Wireless Headphones',
            description: 'High-quality wireless headphones with noise cancellation',
            price: 299.99,
            originalPrice: 399.99,
            images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.8,
            reviewCount: 124,
            inStock: true,
            stockQuantity: 50,
            seller: {
              _id: 'seller1',
              name: 'TechStore Pro',
              rating: 4.9,
              totalSales: 1250
            },
            tags: ['featured', 'bestseller'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '2',
            title: 'Smart Fitness Watch',
            description: 'Advanced fitness tracking with heart rate monitor',
            price: 199.99,
            originalPrice: 249.99,
            images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.6,
            reviewCount: 89,
            inStock: true,
            stockQuantity: 30,
            seller: {
              _id: 'seller2',
              name: 'FitTech Solutions',
              rating: 4.7,
              totalSales: 890
            },
            tags: ['featured', 'new'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '3',
            title: 'Organic Cotton T-Shirt',
            description: 'Comfortable and sustainable organic cotton t-shirt',
            price: 29.99,
            originalPrice: 39.99,
            images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop'],
            category: 'clothing',
            rating: 4.5,
            reviewCount: 67,
            inStock: true,
            stockQuantity: 100,
            seller: {
              _id: 'seller3',
              name: 'EcoFashion',
              rating: 4.8,
              totalSales: 560
            },
            tags: ['featured', 'eco-friendly'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '4',
            title: 'Professional Camera Lens',
            description: '85mm f/1.4 portrait lens for professional photography',
            price: 899.99,
            originalPrice: 1099.99,
            images: ['https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.9,
            reviewCount: 45,
            inStock: true,
            stockQuantity: 15,
            seller: {
              _id: 'seller4',
              name: 'PhotoGear Pro',
              rating: 4.9,
              totalSales: 320
            },
            tags: ['featured', 'professional'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '5',
            title: 'Ergonomic Office Chair',
            description: 'Comfortable ergonomic chair for long work sessions',
            price: 449.99,
            originalPrice: 599.99,
            images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'],
            category: 'home',
            rating: 4.7,
            reviewCount: 156,
            inStock: true,
            stockQuantity: 25,
            seller: {
              _id: 'seller5',
              name: 'Office Solutions',
              rating: 4.6,
              totalSales: 780
            },
            tags: ['featured', 'ergonomic'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '6',
            title: 'Wireless Gaming Mouse',
            description: 'High-precision wireless gaming mouse with RGB lighting',
            price: 79.99,
            originalPrice: 99.99,
            images: ['https://images.unsplash.com/photo-1527814050087-3793815479db?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.4,
            reviewCount: 203,
            inStock: true,
            stockQuantity: 75,
            seller: {
              _id: 'seller6',
              name: 'GameTech',
              rating: 4.5,
              totalSales: 1100
            },
            tags: ['featured', 'gaming'],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];
        
        setFeaturedProducts(mockProducts);
      } catch (error) {
        console.error('Failed to fetch featured products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  const totalPages = Math.ceil(featuredProducts.length / productsPerPage);
  const currentProducts = featuredProducts.slice(
    currentPage * productsPerPage,
    (currentPage + 1) * productsPerPage
  );

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const handleProductClick = (productId: string) => {
    router.push(`/products/${productId}`);
  };

  const handleAddToCart = (product: HomeProduct, e: React.MouseEvent) => {
    e.stopPropagation();
    // Convert HomeProduct to Product-like object for cart
    const productForCart = {
      id: product._id!,
      name: product.title,
      slug: product.title.toLowerCase().replace(/\s+/g, '-'),
      images: product.images,
      base_price: product.price,
    } as any;

    addItem(productForCart);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-80 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('featured.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('featured.description')}
          </p>
        </div>

        <div className="relative">
          {/* Navigation Buttons */}
          {totalPages > 1 && (
            <>
              <Button
                variant="outline"
                size="icon"
                className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl"
                onClick={prevPage}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl"
                onClick={nextPage}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentProducts.map((product) => (
              <Card
                key={product._id}
                className="group cursor-pointer hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
                onClick={() => handleProductClick(product._id!)}
              >
                <CardContent className="p-0">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={product.images[0] || '/images/placeholder-product.jpg'}
                      alt={product.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/placeholder-product.jpg';
                      }}
                    />
                    {product.originalPrice && product.originalPrice > product.price && (
                      <Badge className="absolute top-2 left-2 bg-red-500">
                        {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute top-2 right-2 bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Add to wishlist functionality
                      }}
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
                <CardFooter className="p-4 space-y-3">
                  <div className="w-full">
                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors">
                      {product.title}
                    </h3>
                    <div className="flex items-center space-x-1 mt-1">
                      {renderStars(product.rating)}
                      <span className="text-sm text-gray-500">({product.reviewCount})</span>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">${product.price}</span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                        )}
                      </div>
                      <Button
                        size="sm"
                        onClick={(e) => handleAddToCart(product, e)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Dots Indicator */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: totalPages }).map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                    index === currentPage ? 'bg-primary' : 'bg-gray-300'
                  }`}
                  onClick={() => setCurrentPage(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* View All Products Button */}
        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            onClick={() => router.push('/products')}
            className="px-8"
          >
            {t('featured.viewAll')}
          </Button>
        </div>
      </div>
    </section>
  );
}

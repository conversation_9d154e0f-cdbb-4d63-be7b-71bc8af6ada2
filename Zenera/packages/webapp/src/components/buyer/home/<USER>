"use client";

import { <PERSON> } from './hero';
import { CategoryCarousel } from './category-carousel';
import { FeaturedProducts } from './featured-products';
import { AllProducts } from './all-products';
import { NewsletterSection } from './newsletter-section';

interface BuyerHomePageProps {
  locale: string;
}

export function BuyerHomePage({ locale }: BuyerHomePageProps) {
  return (
    <div className="min-h-screen">{/* Components will be rendered here */}

      <Hero locale={locale} />
      <CategoryCarousel locale={locale} />
      <FeaturedProducts locale={locale} />
      <AllProducts locale={locale} />
      <NewsletterSection locale={locale} />
    </div>
  );
}

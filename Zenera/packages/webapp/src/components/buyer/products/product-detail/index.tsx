"use client";

import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ImageGallery } from './image-gallery';
import { ProductInfo } from './product-info';
import { ProductTabs } from './product-tabs';
import { RelatedProducts } from './related-products';
import { ShopInfo } from './shop-info';
import { ShopProducts } from './shop-products';
import type { Product as BaseProduct, ProductVariant } from '@zenera/sharing';

// Extended Product interface for product detail page
interface ProductDetail extends BaseProduct {
  category: string; // Display name for category
  sale_price?: number;
  in_stock: boolean;
  stock_quantity: number;
  sku: string;
  specifications: Record<string, string>;
  rating: number; // For compatibility
  reviews_count: number; // For compatibility
  variants?: Array<{
    id: string;
    name: string;
    price: number;
    in_stock: boolean;
  }>;
}

interface RelatedProduct {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface Shop {
  id: string;
  name: string;
  slug: string;
  description: string;
  logo: string;
  banner: string;
  category: string;
  rating: number;
  reviews_count: number;
  products_count: number;
  followers_count: number;
  verified: boolean;
  location: string;
  established: string;
}

interface ShopProduct {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface ProductDetailPageProps {
  product: ProductDetail;
  locale: string;
  relatedProducts?: RelatedProduct[];
  shop?: Shop;
  shopProducts?: ShopProduct[];
}

export function ProductDetailPage({ product, locale, relatedProducts = [], shop, shopProducts = [] }: ProductDetailPageProps) {
  const { t } = useZeneraTranslation('product-detail');

  const breadcrumbItems = [
    { label: t('breadcrumb.home'), href: `/${locale}` },
    { label: t('breadcrumb.products'), href: `/${locale}/products` },
    { label: product.category, href: `/${locale}/products?category=${product.category.toLowerCase()}` },
    { label: product.name },
  ];

  const discount = product.sale_price && product.base_price ? Math.round(((product.base_price - product.sale_price) / product.base_price) * 100) : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <SimpleBreadcrumb items={breadcrumbItems} className="mb-6" />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <ImageGallery 
            images={product.images}
            productName={product.name}
            discount={discount}
          />

          {/* Product Info */}
          <ProductInfo 
            product={product}
            locale={locale}
          />
        </div>

        {/* Product Details Tabs */}
        <ProductTabs specifications={product.specifications} />

        {/* Shop Information */}
        {shop && (
          <ShopInfo
            shop={shop}
            locale={locale}
          />
        )}

        {/* Shop Products */}
        {shop && shopProducts.length > 0 && (
          <ShopProducts
            products={shopProducts}
            shopName={shop.name}
            shopSlug={shop.slug}
            locale={locale}
          />
        )}

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <RelatedProducts
            products={relatedProducts}
            locale={locale}
            title={t('relatedProducts.title')}
          />
        )}
      </div>
    </div>
  );
}

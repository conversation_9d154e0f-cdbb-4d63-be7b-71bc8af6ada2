"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Package, ArrowLeft } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface OrdersHeaderProps {
  locale: string;
  totalOrders: number;
}

export function OrdersHeader({ locale, totalOrders }: OrdersHeaderProps) {
  const { t } = useZeneraTranslation('orders');

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-blue-100 rounded-xl">
          <Package className="w-8 h-8 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            {t('title')}
          </h1>
          <p className="text-gray-600 mt-1">
            {t('subtitle')} ({totalOrders} {t('items').toLowerCase()})
          </p>
        </div>
      </div>

      <Link href={`/${locale}/products`}>
        <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
          <ArrowLeft className="w-4 h-4" />
          {t('startShopping')}
        </Button>
      </Link>
    </div>
  );
}

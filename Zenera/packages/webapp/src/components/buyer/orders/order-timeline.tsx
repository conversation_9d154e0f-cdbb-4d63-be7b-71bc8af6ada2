"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  Clock, 
  Package, 
  Truck, 
  MapPin,
  Calendar
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Order } from '@zenera/sharing';

interface OrderTimelineProps {
  order: Order;
  locale: string;
}

interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  status: 'completed' | 'current' | 'pending';
  icon: React.ComponentType<any>;
}

export function OrderTimeline({ order, locale }: OrderTimelineProps) {
  const { t } = useZeneraTranslation('orders');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Mock timeline events based on order status
  const getTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [
      {
        id: 'placed',
        title: t('orderPlaced'),
        description: t('orderPlacedDescription', 'Your order has been placed successfully'),
        date: order.created_at,
        status: 'completed',
        icon: CheckCircle
      }
    ];

    if (order.status !== 'cancelled') {
      events.push({
        id: 'confirmed',
        title: t('orderConfirmed'),
        description: t('orderConfirmedDescription', 'Your order has been confirmed and is being prepared'),
        date: order.created_at, // Mock: same as created for now
        status: ['confirmed', 'processing', 'shipped', 'delivered'].includes(order.status) ? 'completed' : 'pending',
        icon: CheckCircle
      });

      events.push({
        id: 'processing',
        title: t('orderProcessing', 'Processing'),
        description: t('orderProcessingDescription', 'Your order is being prepared for shipment'),
        date: order.created_at, // Mock date
        status: ['processing', 'shipped', 'delivered'].includes(order.status) ? 'completed' : 
                order.status === 'confirmed' ? 'current' : 'pending',
        icon: Package
      });

      events.push({
        id: 'shipped',
        title: t('orderShipped'),
        description: order.tracking_number 
          ? t('orderShippedWithTracking', { trackingNumber: order.tracking_number })
          : t('orderShippedDescription', 'Your order has been shipped'),
        date: order.updated_at,
        status: ['shipped', 'delivered'].includes(order.status) ? 'completed' : 
                order.status === 'processing' ? 'current' : 'pending',
        icon: Truck
      });

      events.push({
        id: 'delivered',
        title: t('orderDelivered'),
        description: t('orderDeliveredDescription', 'Your order has been delivered successfully'),
        date: '', // Mock: not delivered yet
        status: order.status === 'delivered' ? 'completed' : 
                order.status === 'shipped' ? 'current' : 'pending',
        icon: MapPin
      });
    } else {
      events.push({
        id: 'cancelled',
        title: t('orderCancelled'),
        description: t('orderCancelledDescription', 'Your order has been cancelled'),
        date: order.updated_at,
        status: 'completed',
        icon: CheckCircle
      });
    }

    return events;
  };

  const timelineEvents = getTimelineEvents();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'current':
        return 'text-blue-600 bg-blue-100';
      case 'pending':
        return 'text-gray-400 bg-gray-100';
      default:
        return 'text-gray-400 bg-gray-100';
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          {t('orderHistory')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {timelineEvents.map((event, index) => {
            const Icon = event.icon;
            const isLast = index === timelineEvents.length - 1;
            
            return (
              <div key={event.id} className="relative flex gap-4">
                {/* Timeline Line */}
                {!isLast && (
                  <div className="absolute left-6 top-12 w-0.5 h-6 bg-gray-200"></div>
                )}
                
                {/* Icon */}
                <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${getStatusColor(event.status)}`}>
                  <Icon className="w-6 h-6" />
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{event.title}</h4>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${
                        event.status === 'completed' ? 'bg-green-100 text-green-800' :
                        event.status === 'current' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {event.status === 'completed' ? t('completed', 'Completed') :
                       event.status === 'current' ? t('inProgress', 'In Progress') :
                       t('pending')}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                  
                  {event.date && (
                    <p className="text-xs text-gray-500">{formatDate(event.date)}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Tracking Information */}
        {order.tracking_number && order.status === 'shipped' && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-blue-900">{t('trackingNumber')}</span>
            </div>
            <p className="text-sm text-blue-800 font-mono">{order.tracking_number}</p>
            <p className="text-xs text-blue-600 mt-1">
              {t('estimatedDelivery')}: 2-3 business days
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Package, ArrowLeft, MessageCircle } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface OrderNotFoundProps {
  locale: string;
  orderId: string;
}

export function OrderNotFound({ locale, orderId }: OrderNotFoundProps) {
  const { t } = useZeneraTranslation('orders');

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="max-w-2xl mx-auto bg-white/80 backdrop-blur-sm border-0 shadow-xl">
        <CardContent className="p-6 sm:p-12 text-center">
          {/* Not Found Icon */}
          <div className="relative mb-6 sm:mb-8">
            <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto bg-gradient-to-br from-red-100 to-orange-100 rounded-full flex items-center justify-center">
              <Package className="w-12 h-12 sm:w-16 sm:h-16 text-red-600" />
            </div>
            <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs sm:text-sm font-bold">!</span>
            </div>
          </div>

          {/* Not Found Content */}
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            {t('orderNotFound')}
          </h2>
          
          <p className="text-gray-600 text-base sm:text-lg mb-2">
            {t('orderNotFoundDescription')}
          </p>
          
          <p className="text-sm text-gray-500 mb-8">
            Order ID: {orderId}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href={`/${locale}/orders`}>
              <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
                <ArrowLeft className="w-4 h-4" />
                {t('backToOrders')}
              </Button>
            </Link>
            
            <Link href={`/${locale}/support`}>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 w-full sm:w-auto">
                <MessageCircle className="w-4 h-4" />
                {t('contactSupport')}
              </Button>
            </Link>
          </div>

          {/* Help Text */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              {t('needHelp')} {' '}
              <Link href={`/${locale}/support`} className="text-blue-600 hover:text-blue-700 font-medium">
                {t('contactSupport')}
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

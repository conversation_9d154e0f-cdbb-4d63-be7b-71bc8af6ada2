"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle, Phone, Mail } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface SupportSectionProps {
  locale: string;
}

export function SupportSection({ locale }: SupportSectionProps) {
  const { t } = useZeneraTranslation('orders');

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          {t('needHelp')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm">
          {t('supportDescription')}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          <Link href={`/${locale}/support/chat`}>
            <Button variant="outline" className="w-full flex items-center gap-2 h-auto py-3">
              <MessageCircle className="w-4 h-4" />
              <div className="text-left">
                <div className="font-medium text-sm">{t('liveChat')}</div>
                <div className="text-xs text-gray-500">24/7 Available</div>
              </div>
            </Button>
          </Link>

          <Link href={`/${locale}/support/email`}>
            <Button variant="outline" className="w-full flex items-center gap-2 h-auto py-3">
              <Mail className="w-4 h-4" />
              <div className="text-left">
                <div className="font-medium text-sm">{t('emailSupport')}</div>
                <div className="text-xs text-gray-500">Response in 24h</div>
              </div>
            </Button>
          </Link>

          <Link href={`/${locale}/support/phone`}>
            <Button variant="outline" className="w-full flex items-center gap-2 h-auto py-3">
              <Phone className="w-4 h-4" />
              <div className="text-left">
                <div className="font-medium text-sm">{t('phoneSupport')}</div>
                <div className="text-xs text-gray-500">Mon-Fri 9-5</div>
              </div>
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

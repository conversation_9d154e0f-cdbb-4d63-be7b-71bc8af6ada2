"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Package, 
  Calendar, 
  MapPin, 
  CreditCard,
  Truck,
  Download,
  MessageCircle,
  Phone,
  Mail
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { OrderNotFound } from './order-not-found';
import { OrderTimeline } from './order-timeline';
import { SupportSection } from './support-section';
import type { Order } from '@zenera/sharing';

interface OrderDetailPageProps {
  locale: string;
  orderId: string;
}

// Mock order data - replace with actual API call
const mockOrder: Order = {
  id: 'ORD-001',
  order_number: 'ZEN-2024-001',
  user_id: 'user-1',
  status: 'shipped',
  total_amount: 299.99,
  currency: 'USD',
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-20T14:45:00Z',
  items: [
    {
      id: 'item-1',
      product_id: 'prod-1',
      product_name: 'Wireless Bluetooth Headphones',
      quantity: 1,
      price: 199.99,
      total: 199.99
    },
    {
      id: 'item-2',
      product_id: 'prod-2',
      product_name: 'Premium Phone Case',
      quantity: 2,
      price: 50.00,
      total: 100.00
    }
  ],
  shipping_address: {
    street: '123 Main Street, Apt 4B',
    city: 'New York',
    state: 'NY',
    postal_code: '10001',
    country: 'US'
  },
  payment_method: 'credit_card',
  tracking_number: 'TRK123456789'
};

export function OrderDetailPage({ locale, orderId }: OrderDetailPageProps) {
  const { t } = useZeneraTranslation('orders');
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock API call
    const fetchOrder = async () => {
      setIsLoading(true);
      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock: return order if ID matches, otherwise null
        if (orderId === 'ORD-001') {
          setOrder(mockOrder);
        } else {
          setOrder(null);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [orderId]);

  const breadcrumbItems = [
    { label: t('common.home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/orders` },
    { label: order ? t('orderNumber', { number: order.order_number }) : orderId, href: `/${locale}/orders/${orderId}` }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const subtotal = order?.items.reduce((sum, item) => sum + item.total, 0) || 0;
  const shipping = 9.99;
  const tax = subtotal * 0.1;
  const total = subtotal + shipping + tax;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!order) {
    return <OrderNotFound locale={locale} orderId={orderId} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}/orders`}>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                {t('backToOrders')}
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                {t('orderNumber', { number: order.order_number })}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getStatusColor(order.status)}>
                  {t(order.status)}
                </Badge>
                <span className="text-gray-600">
                  {t('orderDate')}: {formatDate(order.created_at)}
                </span>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            {order.tracking_number && (
              <Button variant="outline" className="flex items-center gap-2">
                <Truck className="w-4 h-4" />
                {t('trackOrder')}
              </Button>
            )}
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              {t('downloadInvoice')}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Timeline */}
            <OrderTimeline order={order} locale={locale} />

            {/* Order Items */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  {t('items')} ({order.items.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center py-3 border-b border-gray-100 last:border-0">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                      <p className="text-sm text-gray-600">
                        {t('quantity')}: {item.quantity} × ${item.price.toFixed(2)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${item.total.toFixed(2)}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Support Section */}
            <SupportSection locale={locale} />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle>{t('orderSummary')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{t('subtotal')}</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{t('shipping')}</span>
                  <span>${shipping.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{t('tax')}</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>{t('orderTotal')}</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  {t('shippingAddress')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>{order.shipping_address.street}</p>
                  <p>{order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}</p>
                  <p>{order.shipping_address.country}</p>
                </div>
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  {t('paymentMethod')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 capitalize">
                  {order.payment_method.replace('_', ' ')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

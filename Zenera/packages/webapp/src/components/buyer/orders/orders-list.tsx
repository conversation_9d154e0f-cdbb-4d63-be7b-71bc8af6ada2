"use client";

import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Package, 
  Calendar, 
  DollarSign, 
  Eye, 
  Truck, 
  RotateCcw,
  Download,
  X
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Order } from '@zenera/sharing';

interface OrdersListProps {
  orders: Order[];
  locale: string;
}

export function OrdersList({ orders, locale }: OrdersListProps) {
  const { t } = useZeneraTranslation('orders');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'returned':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getActionButtons = (order: Order) => {
    const buttons = [];

    // View Details - always available
    buttons.push(
      <Link key="view" href={`/${locale}/orders/${order.id}`}>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Eye className="w-4 h-4" />
          {t('viewDetails')}
        </Button>
      </Link>
    );

    // Track Order - for shipped orders
    if (order.status === 'shipped' && order.tracking_number) {
      buttons.push(
        <Button key="track" variant="outline" size="sm" className="flex items-center gap-1">
          <Truck className="w-4 h-4" />
          {t('trackOrder')}
        </Button>
      );
    }

    // Reorder - for delivered orders
    if (order.status === 'delivered') {
      buttons.push(
        <Button key="reorder" variant="outline" size="sm" className="flex items-center gap-1">
          <RotateCcw className="w-4 h-4" />
          {t('reorder')}
        </Button>
      );
    }

    // Cancel Order - for pending/confirmed orders
    if (order.status === 'pending' || order.status === 'confirmed') {
      buttons.push(
        <Button key="cancel" variant="outline" size="sm" className="flex items-center gap-1 text-red-600 hover:text-red-700">
          <X className="w-4 h-4" />
          {t('cancelOrder')}
        </Button>
      );
    }

    // Download Invoice - for delivered orders
    if (order.status === 'delivered') {
      buttons.push(
        <Button key="invoice" variant="outline" size="sm" className="flex items-center gap-1">
          <Download className="w-4 h-4" />
          {t('downloadInvoice')}
        </Button>
      );
    }

    return buttons;
  };

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('noOrders')}</h3>
        <p className="text-gray-600">{t('noOrdersDescription')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <Card key={order.id} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              {/* Order Info */}
              <div className="flex-1 space-y-3">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {t('orderNumber', { number: order.order_number })}
                  </h3>
                  <Badge className={`w-fit ${getStatusColor(order.status)}`}>
                    {t(order.status)}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{t('orderDate')}: {formatDate(order.created_at)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-gray-600">
                    <Package className="w-4 h-4" />
                    <span>{order.items.length} {t('items')}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-gray-600">
                    <DollarSign className="w-4 h-4" />
                    <span className="font-medium text-gray-900">
                      ${order.total_amount.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Order Items Preview */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">{t('items')}:</p>
                  <div className="space-y-1">
                    {order.items.slice(0, 2).map((item) => (
                      <div key={item.id} className="flex justify-between text-sm text-gray-600">
                        <span>{item.product_name} x{item.quantity}</span>
                        <span>${item.total.toFixed(2)}</span>
                      </div>
                    ))}
                    {order.items.length > 2 && (
                      <p className="text-sm text-gray-500">
                        +{order.items.length - 2} {t('items').toLowerCase()}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-2 lg:flex-col lg:w-48">
                {getActionButtons(order)}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

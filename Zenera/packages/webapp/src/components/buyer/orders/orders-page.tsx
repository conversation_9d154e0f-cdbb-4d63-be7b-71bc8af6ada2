"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Package, Search, Filter, ArrowLeft } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { OrdersHeader } from './orders-header';
import { OrdersList } from './orders-list';
import { EmptyOrders } from './empty-orders';
import type { Order } from '@zenera/sharing';

interface OrdersPageProps {
  locale: string;
}

// Mock orders data
const mockOrders: Order[] = [
  {
    id: 'ORD-001',
    order_number: 'ZEN-2024-001',
    user_id: 'user-1',
    status: 'delivered',
    total_amount: 299.99,
    currency: 'USD',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z',
    items: [
      {
        id: 'item-1',
        product_id: 'prod-1',
        product_name: 'Wireless Headphones',
        quantity: 1,
        price: 199.99,
        total: 199.99
      },
      {
        id: 'item-2',
        product_id: 'prod-2',
        product_name: 'Phone Case',
        quantity: 2,
        price: 50.00,
        total: 100.00
      }
    ],
    shipping_address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'US'
    },
    payment_method: 'credit_card',
    tracking_number: 'TRK123456789'
  },
  {
    id: 'ORD-002',
    order_number: 'ZEN-2024-002',
    user_id: 'user-1',
    status: 'shipped',
    total_amount: 149.99,
    currency: 'USD',
    created_at: '2024-01-10T09:15:00Z',
    updated_at: '2024-01-12T16:20:00Z',
    items: [
      {
        id: 'item-3',
        product_id: 'prod-3',
        product_name: 'Bluetooth Speaker',
        quantity: 1,
        price: 149.99,
        total: 149.99
      }
    ],
    shipping_address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'US'
    },
    payment_method: 'paypal',
    tracking_number: 'TRK987654321'
  }
];

export function OrdersPage({ locale }: OrdersPageProps) {
  const { t } = useZeneraTranslation('orders');
  const [orders] = useState<Order[]>(mockOrders);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>(mockOrders);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const breadcrumbItems = [
    { label: t('common.home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/orders` }
  ];

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterOrders(query, statusFilter, sortBy);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    filterOrders(searchQuery, status, sortBy);
  };

  const handleSort = (sort: string) => {
    setSortBy(sort);
    filterOrders(searchQuery, statusFilter, sort);
  };

  const filterOrders = (query: string, status: string, sort: string) => {
    let filtered = [...orders];

    // Filter by search query
    if (query) {
      filtered = filtered.filter(order => 
        order.order_number.toLowerCase().includes(query.toLowerCase()) ||
        order.items.some(item => 
          item.product_name.toLowerCase().includes(query.toLowerCase())
        )
      );
    }

    // Filter by status
    if (status !== 'all') {
      filtered = filtered.filter(order => order.status === status);
    }

    // Sort orders
    filtered.sort((a, b) => {
      switch (sort) {
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'highestAmount':
          return b.total_amount - a.total_amount;
        case 'lowestAmount':
          return a.total_amount - b.total_amount;
        case 'newest':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    setFilteredOrders(filtered);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Content */}
      <div className="container mx-auto px-4 py-8">
        <OrdersHeader locale={locale} totalOrders={orders.length} />

        {orders.length === 0 ? (
          <EmptyOrders locale={locale} />
        ) : (
          <div className="space-y-6">
            {/* Filters and Search */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder={t('searchOrders')}
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                  <SelectTrigger className="w-full sm:w-48">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder={t('filterByStatus')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('allOrders')}</SelectItem>
                    <SelectItem value="pending">{t('pending')}</SelectItem>
                    <SelectItem value="confirmed">{t('confirmed')}</SelectItem>
                    <SelectItem value="processing">{t('processing')}</SelectItem>
                    <SelectItem value="shipped">{t('shipped')}</SelectItem>
                    <SelectItem value="delivered">{t('delivered')}</SelectItem>
                    <SelectItem value="cancelled">{t('cancelled')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort */}
              <Select value={sortBy} onValueChange={handleSort}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder={t('sortBy')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">{t('newest')}</SelectItem>
                  <SelectItem value="oldest">{t('oldest')}</SelectItem>
                  <SelectItem value="highestAmount">{t('highestAmount')}</SelectItem>
                  <SelectItem value="lowestAmount">{t('lowestAmount')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Orders List */}
            <OrdersList orders={filteredOrders} locale={locale} />
          </div>
        )}
      </div>
    </div>
  );
}

"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Product } from '@zenera/sharing/types';

// Define local types for now
interface CartItem {
  id: string;
  product_id: string;
  variant_id?: string;
  quantity: number;
  price: number;
  total: number;
  product_name: string;
  product_slug: string;
  product_image: string;
  is_available: boolean;
  product?: {
    id: string;
    name: string;
    slug: string;
    images: string[];
    price: number;
  };
  variant?: {
    id: string;
    name: string;
    price: number;
    attributes: Record<string, string>;
  };
}

interface CartSummary {
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  items_count: number;
}

interface ProductVariant {
  id: string;
  name: string;
  price: number;
  attributes: Record<string, string>;
}

interface CartState {
  // State
  items: CartItem[];
  summary: CartSummary;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
  
  // Cart management actions
  addItem: (product: Product, variant?: ProductVariant, quantity?: number) => Promise<void>;
  updateItemQuantity: (productId: string, variantId: string | undefined, quantity: number) => Promise<void>;
  removeItem: (productId: string, variantId?: string) => Promise<void>;
  clearCart: () => Promise<void>;
  
  // Cart operations
  getItem: (productId: string, variantId?: string) => CartItem | undefined;
  getItemCount: () => number;
  getTotalPrice: () => number;
  getTotalItems: () => number; // Alias for getItemCount
  isItemInCart: (productId: string, variantId?: string) => boolean;
  updateQuantity: (productId: string, quantity: number) => void; // Simplified quantity update
  
  // Sync operations (to be implemented when needed)
  syncWithServer: () => Promise<void>;
  loadCartFromServer: () => Promise<void>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  updateSummary: () => void;
  reset: () => void;
}

const initialSummary: CartSummary = {
  subtotal: 0,
  tax: 0,
  shipping: 0,
  discount: 0,
  total: 0,
  items_count: 0,
};

const initialState = {
  items: [],
  summary: initialSummary,
  isLoading: false,
  error: null,
  lastUpdated: Date.now(),
};

export const useCartStore = create<CartState>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // Add item to cart
      addItem: async (product, variant, quantity = 1) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const productId = (product as any)._id || product.id || '';
          const variantId = (variant as any)?._id;
          const price = variant?.price || (product as any).base_price || 0;
          
          set((state) => {
            const existingItemIndex = state.items.findIndex(
              item => item.product_id === productId && item.variant_id === variantId
            );

            if (existingItemIndex !== -1) {
              // Update existing item
              state.items[existingItemIndex].quantity += quantity;
            } else {
              // Add new item
              const newItem: CartItem = {
                id: `${productId}-${variantId || 'default'}`,
                product_id: productId,
                variant_id: variantId,
                quantity,
                price,
                total: price * quantity,
                product_name: product.name,
                product_slug: (product as any).slug || '',
                product_image: product.images?.[0] || '',
                is_available: (product as any).is_active !== false,
                product: {
                  id: productId,
                  name: product.name,
                  slug: (product as any).slug || '',
                  images: product.images || [],
                  price: (product as any).base_price || 0,
                },
                variant: variant ? {
                  id: variantId || '',
                  name: variant.name,
                  price: variant.price,
                  attributes: variant.attributes,
                } : undefined,
              };
              state.items.push(newItem);
            }
            
            state.lastUpdated = Date.now();
          });
          
          get().updateSummary();
          
          // TODO: Sync with server
          // await cartApi.addItem(productId, variantId, quantity);
          
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to add item to cart';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Update item quantity
      updateItemQuantity: async (productId, variantId, quantity) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          if (quantity <= 0) {
            get().removeItem(productId, variantId);
            return;
          }

          set((state) => {
            const itemIndex = state.items.findIndex(
              item => item.product_id === productId && item.variant_id === variantId
            );

            if (itemIndex !== -1) {
              state.items[itemIndex].quantity = quantity;
              state.lastUpdated = Date.now();
            }
          });
          
          get().updateSummary();
          
          // TODO: Sync with server
          // await cartApi.updateItemQuantity(productId, variantId, quantity);
          
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update item quantity';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Remove item from cart
      removeItem: async (productId, variantId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          set((state) => {
            state.items = state.items.filter(
              item => !(item.product_id === productId && item.variant_id === variantId)
            );
            state.lastUpdated = Date.now();
          });
          
          get().updateSummary();
          
          // TODO: Sync with server
          // await cartApi.removeItem(productId, variantId);
          
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to remove item from cart';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Clear entire cart
      clearCart: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          set((state) => {
            state.items = [];
            state.lastUpdated = Date.now();
          });
          
          get().updateSummary();
          
          // TODO: Sync with server
          // await cartApi.clearCart();
          
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to clear cart';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Get specific item
      getItem: (productId, variantId) => {
        return get().items.find(
          item => item.product_id === productId && item.variant_id === variantId
        );
      },

      // Get total item count
      getItemCount: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0);
      },

      // Get total price
      getTotalPrice: () => {
        return get().items.reduce((total, item) => total + (item.price * item.quantity), 0);
      },

      // Get total items (alias for getItemCount)
      getTotalItems: () => {
        return get().getItemCount();
      },

      // Check if item is in cart
      isItemInCart: (productId, variantId) => {
        return get().items.some(
          item => item.product_id === productId && item.variant_id === variantId
        );
      },

      // Simplified quantity update
      updateQuantity: (productId, quantity) => {
        set((state) => {
          const itemIndex = state.items.findIndex(item => item.product_id === productId);
          if (itemIndex !== -1) {
            if (quantity <= 0) {
              state.items.splice(itemIndex, 1);
            } else {
              state.items[itemIndex].quantity = quantity;
              state.items[itemIndex].total = state.items[itemIndex].price * quantity;
            }
            state.lastUpdated = Date.now();
          }
        });
        get().updateSummary();
      },



      // Sync with server
      syncWithServer: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          // TODO: Implement API call
          console.log('syncWithServer');
          // await cartApi.syncCart(get().items);
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to sync cart with server';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Load cart from server
      loadCartFromServer: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          // TODO: Implement API call
          console.log('loadCartFromServer');
          // const cartData = await cartApi.getCart();
          // set((state) => {
          //   state.items = cartData.items;
          //   state.lastUpdated = Date.now();
          // });
          // get().updateSummary();
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load cart from server';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Update cart summary
      updateSummary: () => {
        set((state) => {
          const items = state.items;
          const total_items = items.reduce((sum, item) => sum + item.quantity, 0);
          const total_price = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          const shipping_fee = total_price > 500000 ? 0 : 30000; // Free shipping over 500k VND
          const discount_amount = 0; // TODO: Calculate discounts
          const final_total = total_price + shipping_fee - discount_amount;

          state.summary = {
            subtotal: total_price,
            tax: 0,
            shipping: shipping_fee,
            discount: discount_amount,
            total: final_total,
            items_count: total_items,
          };
        });
      },

      // Utility actions
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },

      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },

      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
    })),
    {
      name: 'zenera-cart-store',
      partialize: (state) => ({
        items: state.items,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
);

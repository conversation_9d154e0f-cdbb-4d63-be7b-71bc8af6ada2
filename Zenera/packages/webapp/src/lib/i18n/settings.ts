/**
 * i18n Settings
 * Configuration for internationalization system
 * Inspired by Medoo's i18n settings
 */

import { 
  Languages, 
  DEFAULT_LOCALE, 
  SUPPORTED_LOCALES, 
  DEFAULT_NAMESPACE,
  DEFAULT_NAMESPACES,
  LANGUAGE_DETECTION_ORDER,
  LOCALE_COOKIE_NAME 
} from "@/lib/constants/languages";

export const fallbackLng = DEFAULT_LOCALE;
export const languages = SUPPORTED_LOCALES;
export const defaultNS = DEFAULT_NAMESPACE;
export const cookieName = LOCALE_COOKIE_NAME;

/**
 * Get i18next options
 */
export function getOptions(lng = fallbackLng, ns: string | string[] = defaultNS) {
  return {
    // debug: process.env.NODE_ENV === 'development',
    supportedLngs: languages,
    fallbackLng,
    lng,
    fallbackNS: defaultNS,
    defaultNS,
    ns: Array.isArray(ns) ? ns : [ns],
    interpolation: {
      escapeValue: false, // React already does escaping
    },
    react: {
      useSuspense: false, // Disable suspense for SSR compatibility
    },
  };
}

/**
 * Get client-side options
 */
export function getClientOptions(lng = fallbackLng, ns: string | string[] = defaultNS) {
  return {
    ...getOptions(lng, ns),
    detection: {
      order: LANGUAGE_DETECTION_ORDER,
      lookupCookie: cookieName,
      lookupLocalStorage: 'zenera_locale',
      lookupFromPathIndex: 0,
      lookupFromSubdomainIndex: 0,
      caches: ['localStorage', 'cookie'],
      excludeCacheFor: ['cimode'], // never cache if language is cimode
    },
  };
}

/**
 * Get server-side options
 */
export function getServerOptions(lng = fallbackLng, ns: string | string[] = defaultNS) {
  return {
    ...getOptions(lng, ns),
    // Server-side specific options
    initImmediate: false,
    preload: languages,
  };
}

/**
 * Resource loading configuration
 */
export const resourceConfig = {
  loadPath: '/locales/{{lng}}/{{ns}}.json',
  addPath: '/locales/{{lng}}/{{ns}}.json',
};

/**
 * Default namespaces to load
 */
export const defaultNamespaces = DEFAULT_NAMESPACES;

/**
 * Namespace configurations
 */
export const namespaceConfigs = {
  translation: {
    name: 'translation',
    description: 'General translations',
    required: true,
  },
  validation: {
    name: 'validation',
    description: 'Form validation messages',
    required: true,
  },
  form: {
    name: 'form',
    description: 'Form labels and placeholders',
    required: true,
  },
  common: {
    name: 'common',
    description: 'Common UI elements',
    required: true,
  },
  auth: {
    name: 'auth',
    description: 'Authentication related',
    required: false,
  },
  product: {
    name: 'product',
    description: 'Product related',
    required: false,
  },
  order: {
    name: 'order',
    description: 'Order related',
    required: false,
  },
  cart: {
    name: 'cart',
    description: 'Shopping cart related',
    required: false,
  },
  checkout: {
    name: 'checkout',
    description: 'Checkout process related',
    required: false,
  },
  error: {
    name: 'error',
    description: 'Error messages',
    required: false,
  },
};

/**
 * Get required namespaces
 */
export function getRequiredNamespaces(): string[] {
  return Object.values(namespaceConfigs)
    .filter(config => config.required)
    .map(config => config.name);
}

/**
 * Get all available namespaces
 */
export function getAllNamespaces(): string[] {
  return Object.values(namespaceConfigs).map(config => config.name);
}

/**
 * Validate namespace
 */
export function isValidNamespace(namespace: string): boolean {
  return Object.keys(namespaceConfigs).includes(namespace);
}

/**
 * Get namespace config
 */
export function getNamespaceConfig(namespace: string) {
  return (namespaceConfigs as any)[namespace] || null;
}

/**
 * Language validation
 */
export function isValidLanguage(language: string): boolean {
  return languages.includes(language as Languages);
}

/**
 * Get language config
 */
export function getLanguageConfig(language: string) {
  if (!isValidLanguage(language)) return null;
  
  return {
    code: language,
    name: language === Languages.EN ? 'English' : 'Tiếng Việt',
    nativeName: language === Languages.EN ? 'English' : 'Tiếng Việt',
    flag: language === Languages.EN ? '🇺🇸' : '🇻🇳',
    rtl: false,
  };
}

/**
 * Development helpers
 */
export const devConfig = {
  showMissingKeys: process.env.NODE_ENV === 'development',
  logLevel: process.env.NODE_ENV === 'development' ? 'warn' : 'error',
  saveMissing: process.env.NODE_ENV === 'development',
};

/**
 * Production optimizations
 */
export const prodConfig = {
  cleanCode: true,
  removeUnusedKeys: true,
  minify: true,
};

/**
 * Export configuration object
 */
export const i18nConfig = {
  fallbackLng,
  languages,
  defaultNS,
  cookieName,
  defaultNamespaces,
  resourceConfig,
  getOptions,
  getClientOptions,
  getServerOptions,
  getRequiredNamespaces,
  getAllNamespaces,
  isValidNamespace,
  isValidLanguage,
  getLanguageConfig,
  devConfig,
  prodConfig,
};

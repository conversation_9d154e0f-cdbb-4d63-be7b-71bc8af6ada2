import type { Metadata } from 'next';
import { CheckoutPage } from '@/components/buyer/checkout/checkout-page';

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;

  return {
    title: 'Checkout - Zenera',
    description: 'Complete your purchase securely. Fast checkout process with multiple payment options.',
    openGraph: {
      title: 'Checkout - Zenera',
      description: 'Complete your purchase securely. Fast checkout process with multiple payment options.',
      type: 'website',
    },
  };
}

export default async function CheckoutPageRoute({ params }: PageProps) {
  const { locale } = await params;
  return <CheckoutPage locale={locale} />;
}

// Force dynamic rendering to avoid build issues
export const dynamic = 'force-dynamic';

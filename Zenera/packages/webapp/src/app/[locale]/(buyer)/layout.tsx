import type { ReactNode } from 'react';
import { Navbar } from '@/components/buyer/layout/navbar';
import { Footer } from '@/components/buyer/layout/footer';

interface BuyerLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

export default function BuyerLayout({ children, params }: BuyerLayoutProps) {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Navbar locale={params.locale} />
      <main className="flex-grow">{children}</main>
      <Footer locale={params.locale} />
    </div>
  );
}

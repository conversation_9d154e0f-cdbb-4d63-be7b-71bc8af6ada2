import { Metadata } from 'next';
import { CartPage } from '@/components/buyer/cart/cart-page';

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return {
    title: 'Shopping Cart - Zenera',
    description: 'Review your selected items and proceed to checkout. Secure shopping experience with Zenera.',
    openGraph: {
      title: 'Shopping Cart - Zenera',
      description: 'Review your selected items and proceed to checkout. Secure shopping experience with Zenera.',
      type: 'website',
    },
  };
}

export default async function CartPageRoute({ params }: PageProps) {
  const { locale } = await params;
  return <CartPage locale={locale} />;
}

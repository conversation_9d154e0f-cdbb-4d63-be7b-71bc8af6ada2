import { Metadata } from 'next';
import { ShopProductsPage } from '@/components/buyer/shops/shop-products-page';

interface PageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  // In a real app, you would fetch shop data here
  const { slug } = await params;
  const shopName = slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return {
    title: `${shopName} Products - Zenera`,
    description: `Browse all products from ${shopName}. Find the best deals and latest items.`,
    openGraph: {
      title: `${shopName} Products - Zenera`,
      description: `Browse all products from ${shopName}. Find the best deals and latest items.`,
      type: 'website',
    },
  };
}

export default async function ShopProductsPageRoute({ params }: PageProps) {
  const { locale, slug } = await params;
  return <ShopProductsPage locale={locale} shopSlug={slug} />;
}

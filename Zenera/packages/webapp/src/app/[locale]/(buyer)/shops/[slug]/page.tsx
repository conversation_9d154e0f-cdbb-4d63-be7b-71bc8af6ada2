import { Suspense } from 'react';
import { ShopDetailPage } from '@/components/buyer/shops/shop-detail';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { notFound } from 'next/navigation';
import { ShopStatus, ShopVerificationStatus } from '@zenera/sharing';

interface ShopDetailPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
  searchParams: Promise<{
    tab?: string;
    category?: string;
    sort?: string;
    page?: string;
  }>;
}

export default async function ShopDetail({ params, searchParams }: ShopDetailPageProps) {
  const { locale, slug } = await params;
  const filters = await searchParams;
  
  // Mock shop data - will be replaced with API call
  const mockShop = {
    id: 'shop-1',
    _id: 'shop-1',
    name: 'TechWorld Store',
    slug: 'techworld-store',
    description: 'Your one-stop destination for the latest technology and gadgets. We provide high-quality electronics with excellent customer service and fast shipping worldwide.',
    logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200',
    banner: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200',
    owner_id: 'user-1',
    category: 'Electronics',
    address: {
      street: '123 Tech Street',
      city: 'Ho Chi Minh City',
      state: 'Ho Chi Minh',
      country: 'Vietnam',
      postal_code: '700000',
      latitude: 10.8231,
      longitude: 106.6297,
    },
    contact: {
      phone: '+84 123 456 789',
      email: '<EMAIL>',
      website: 'https://techworld.com',
      social_media: {
        facebook: 'https://facebook.com/techworld',
        instagram: 'https://instagram.com/techworld',
        twitter: 'https://twitter.com/techworld',
      },
    },
    status: ShopStatus.ACTIVE,
    verification_status: ShopVerificationStatus.VERIFIED,
    settings: {
      auto_accept_orders: true,
      processing_time: 2,
      return_policy: '30-day return policy',
      shipping_policy: 'Free shipping on orders over $50',
      terms_of_service: 'Standard terms apply',
      privacy_policy: 'We protect your privacy',
    },
    stats: {
      total_products: 450,
      total_orders: 2850,
      total_revenue: 125000,
      avg_rating: 4.8,
      total_reviews: 1250,
      followers_count: 15600,
      response_rate: 98,
      response_time: 2,
    },
    established_date: new Date('2018-01-15'),
    is_featured: true,
    tags: ['electronics', 'gadgets', 'tech', 'mobile', 'computers'],
    business_license: 'BL-*********',
    tax_id: 'TX-*********',
    created_at: new Date('2018-01-15'),
    updated_at: new Date(),
  };

  // Mock featured products
  const mockFeaturedProducts = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      slug: 'premium-wireless-headphones',
      base_price: 299.99,
      sale_price: 249.99,
      images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500'],
      category: 'Electronics',
      rating: 4.5,
      reviews_count: 128,
      in_stock: true,
    },
    {
      id: '2',
      name: 'Smart Watch Series X',
      slug: 'smart-watch-series-x',
      base_price: 399.99,
      sale_price: 349.99,
      images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500'],
      category: 'Electronics',
      rating: 4.7,
      reviews_count: 89,
      in_stock: true,
    },
    {
      id: '3',
      name: 'Wireless Charging Pad',
      slug: 'wireless-charging-pad',
      base_price: 49.99,
      sale_price: 39.99,
      images: ['https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=500'],
      category: 'Electronics',
      rating: 4.3,
      reviews_count: 156,
      in_stock: true,
    },
    {
      id: '4',
      name: 'Bluetooth Speaker Pro',
      slug: 'bluetooth-speaker-pro',
      base_price: 129.99,
      images: ['https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=500'],
      category: 'Electronics',
      rating: 4.6,
      reviews_count: 203,
      in_stock: true,
    },
  ];

  // Check if shop exists (in real app, this would be an API call)
  if (slug !== mockShop.slug) {
    notFound();
  }
  
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ShopDetailPage 
        shop={mockShop}
        featuredProducts={mockFeaturedProducts}
        locale={locale}
        filters={filters}
      />
    </Suspense>
  );
}

{"title": "My Orders", "subtitle": "Track and manage your orders", "noOrders": "No orders found", "noOrdersDescription": "You haven't placed any orders yet. Start shopping to see your orders here.", "startShopping": "Start Shopping", "orderNumber": "Order #{{number}}", "orderDate": "Order Date", "status": "Status", "total": "Total", "items": "Items", "viewDetails": "View Details", "trackOrder": "Track Order", "reorder": "Reorder", "cancelOrder": "Cancel Order", "returnOrder": "Return Order", "downloadInvoice": "Download Invoice", "orderDetails": "Order Details", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "discount": "Discount", "orderTotal": "Order Total", "trackingNumber": "Tracking Number", "estimatedDelivery": "Estimated Delivery", "orderHistory": "Order History", "orderPlaced": "Order Placed", "orderConfirmed": "Order Confirmed", "orderShipped": "Order Shipped", "orderDelivered": "Order Delivered", "orderCancelled": "Order Cancelled", "orderReturned": "Order Returned", "orderRefunded": "Order Refunded", "pending": "Pending", "confirmed": "Confirmed", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "returned": "Returned", "refunded": "Refunded", "filterByStatus": "Filter by Status", "allOrders": "All Orders", "searchOrders": "Search orders...", "sortBy": "Sort by", "newest": "Newest", "oldest": "Oldest", "highestAmount": "Highest Amount", "lowestAmount": "Lowest Amount", "orderNotFound": "Order not found", "orderNotFoundDescription": "The order you're looking for doesn't exist or has been removed.", "backToOrders": "Back to Orders", "contactSupport": "Contact Support", "needHelp": "Need help with your order?", "supportDescription": "Our customer support team is here to help you with any questions or concerns.", "liveChat": "Live Chat", "emailSupport": "Email Support", "phoneSupport": "Phone Support"}